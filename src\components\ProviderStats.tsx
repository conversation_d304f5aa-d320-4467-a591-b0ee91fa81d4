'use client';

import { ProviderConfig } from '@/store/contextStore';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  DollarSign, 
  Zap, 
  TrendingUp,
  Activity
} from 'lucide-react';

interface ProviderStatsProps {
  provider: ProviderConfig;
  isArabic: boolean;
}

export default function ProviderStats({ provider, isArabic }: ProviderStatsProps) {
  const stats = provider.stats;
  
  if (!stats || stats.totalRequests === 0) {
    return (
      <div className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
        {isArabic ? 'لا توجد إحصائيات متاحة' : 'No statistics available'}
      </div>
    );
  }

  const successRate = ((stats.successfulRequests / stats.totalRequests) * 100).toFixed(1);
  const avgResponseTime = stats.averageResponseTime.toFixed(0);
  const totalCost = stats.totalCost.toFixed(4);

  const translations = {
    ar: {
      totalRequests: 'إجمالي الطلبات',
      successRate: 'معدل النجاح',
      avgResponseTime: 'متوسط وقت الاستجابة',
      totalCost: 'التكلفة الإجمالية',
      tokensUsed: 'الرموز المستخدمة',
      lastUsed: 'آخر استخدام',
      ms: 'مللي ثانية',
      never: 'لم يستخدم بعد'
    },
    en: {
      totalRequests: 'Total Requests',
      successRate: 'Success Rate',
      avgResponseTime: 'Avg Response Time',
      totalCost: 'Total Cost',
      tokensUsed: 'Tokens Used',
      lastUsed: 'Last Used',
      ms: 'ms',
      never: 'Never used'
    }
  };

  const t = translations[isArabic ? 'ar' : 'en'];

  const formatLastUsed = (date?: Date) => {
    if (!date) return t.never;
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return isArabic ? 'الآن' : 'Just now';
    if (diffMins < 60) return isArabic ? `منذ ${diffMins} دقيقة` : `${diffMins}m ago`;
    if (diffHours < 24) return isArabic ? `منذ ${diffHours} ساعة` : `${diffHours}h ago`;
    return isArabic ? `منذ ${diffDays} يوم` : `${diffDays}d ago`;
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
      {/* إجمالي الطلبات */}
      <div className="flex items-center gap-2">
        <Activity className="w-4 h-4 text-blue-500" />
        <div>
          <div className="text-sm text-gray-600 dark:text-gray-400">{t.totalRequests}</div>
          <div className="font-semibold text-gray-900 dark:text-white">{stats.totalRequests}</div>
        </div>
      </div>

      {/* معدل النجاح */}
      <div className="flex items-center gap-2">
        <CheckCircle className="w-4 h-4 text-green-500" />
        <div>
          <div className="text-sm text-gray-600 dark:text-gray-400">{t.successRate}</div>
          <div className="font-semibold text-gray-900 dark:text-white">{successRate}%</div>
        </div>
      </div>

      {/* متوسط وقت الاستجابة */}
      <div className="flex items-center gap-2">
        <Clock className="w-4 h-4 text-orange-500" />
        <div>
          <div className="text-sm text-gray-600 dark:text-gray-400">{t.avgResponseTime}</div>
          <div className="font-semibold text-gray-900 dark:text-white">{avgResponseTime} {t.ms}</div>
        </div>
      </div>

      {/* التكلفة الإجمالية */}
      <div className="flex items-center gap-2">
        <DollarSign className="w-4 h-4 text-green-600" />
        <div>
          <div className="text-sm text-gray-600 dark:text-gray-400">{t.totalCost}</div>
          <div className="font-semibold text-gray-900 dark:text-white">${totalCost}</div>
        </div>
      </div>

      {/* الرموز المستخدمة */}
      <div className="flex items-center gap-2">
        <Zap className="w-4 h-4 text-purple-500" />
        <div>
          <div className="text-sm text-gray-600 dark:text-gray-400">{t.tokensUsed}</div>
          <div className="font-semibold text-gray-900 dark:text-white">
            {stats.totalTokensUsed.toLocaleString()}
          </div>
        </div>
      </div>

      {/* آخر استخدام */}
      <div className="flex items-center gap-2">
        <TrendingUp className="w-4 h-4 text-indigo-500" />
        <div>
          <div className="text-sm text-gray-600 dark:text-gray-400">{t.lastUsed}</div>
          <div className="font-semibold text-gray-900 dark:text-white text-xs">
            {formatLastUsed(stats.lastUsed)}
          </div>
        </div>
      </div>
    </div>
  );
}
