"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/llm/generate/route";
exports.ids = ["app/api/llm/generate/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_faiss_Desktop_ContextKit_src_app_api_llm_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/llm/generate/route.ts */ \"(rsc)/./src/app/api/llm/generate/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/llm/generate/route\",\n        pathname: \"/api/llm/generate\",\n        filename: \"route\",\n        bundlePath: \"app/api/llm/generate/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\api\\\\llm\\\\generate\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_faiss_Desktop_ContextKit_src_app_api_llm_generate_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/llm/generate/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/llm/generate/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/llm/generate/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/llmProviders */ \"(rsc)/./src/lib/llmProviders.ts\");\n\n\n/**\n * API للتوليد الذكي باستخدام مقدمي خدمات LLM المختلفين\n */ async function POST(request) {\n    try {\n        const { providerId, apiKey, model, messages, context, fieldName, language = \"ar\", temperature = 0.7, maxTokens = 1000, baseUrl } = await request.json();\n        if (!providerId || !apiKey || !messages) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Provider ID, API key, and messages are required\"\n            }, {\n                status: 400\n            });\n        }\n        const provider = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderById)(providerId);\n        if (!provider) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unknown provider\"\n            }, {\n                status: 400\n            });\n        }\n        const finalBaseUrl = baseUrl || provider.baseUrl;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...(0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_1__.getProviderHeaders)(providerId)\n        };\n        // إنشاء system prompt ذكي بناءً على السياق\n        const systemPrompt = createSmartSystemPrompt(context, fieldName, language);\n        const finalMessages = [\n            {\n                role: \"system\",\n                content: systemPrompt\n            },\n            ...messages\n        ];\n        let response;\n        switch(providerId){\n            case \"openai\":\n            case \"openrouter\":\n            case \"deepseek\":\n            case \"groq\":\n                response = await generateOpenAICompatible(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"anthropic\":\n                response = await generateAnthropic(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            case \"google\":\n                response = await generateGoogle(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n                break;\n            default:\n                response = await generateOpenAICompatible(finalBaseUrl, apiKey, headers, model, finalMessages, temperature, maxTokens);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"Generation error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nfunction createSmartSystemPrompt(context, fieldName, language) {\n    const isArabic = language === \"ar\";\n    const basePrompt = isArabic ? `أنت مساعد ذكي متخصص في مساعدة المستخدمين في بناء سياق منظم ومفصل للمشاريع التقنية والإبداعية.` : `You are an AI assistant specialized in helping users build structured and detailed context for technical and creative projects.`;\n    // تقليل حجم السياق - أخذ المعلومات المهمة فقط\n    const relevantContext = extractRelevantContext(context, fieldName);\n    const contextInfo = isArabic ? `معلومات المشروع: ${relevantContext}` : `Project info: ${relevantContext}`;\n    const fieldGuidance = getFieldGuidance(fieldName, isArabic);\n    const instructions = isArabic ? `\nتعليمات:\n1. قدم إجابة مختصرة ومفيدة (2-3 جمل)\n2. استخدم المعلومات المتوفرة لتحسين إجابتك\n3. اجعل الإجابة عملية وقابلة للتطبيق\n4. استخدم اللغة العربية الواضحة\n` : `\nInstructions:\n1. Provide a concise and helpful response (2-3 sentences)\n2. Use available information to improve your answer\n3. Make the response practical and actionable\n4. Use clear language\n`;\n    return `${basePrompt}\\n\\n${contextInfo}\\n\\n${fieldGuidance}\\n\\n${instructions}`;\n}\nfunction extractRelevantContext(context, fieldName) {\n    if (!context) return \"\";\n    // استخراج المعلومات المهمة فقط بدلاً من كامل السياق\n    const relevantFields = [\n        \"name\",\n        \"purpose\",\n        \"targetUsers\",\n        \"goals\"\n    ];\n    const relevant = {};\n    for (const field of relevantFields){\n        if (context[field] && typeof context[field] === \"string\" && context[field].trim()) {\n            relevant[field] = context[field].substring(0, 100); // تقليل طول النص\n        }\n    }\n    return Object.keys(relevant).length > 0 ? JSON.stringify(relevant) : \"مشروع جديد\";\n}\nfunction getFieldGuidance(fieldName, isArabic) {\n    const fieldGuidanceMap = {\n        name: {\n            ar: \"المجال المطلوب: اسم المشروع - قدم اقتراحات لأسماء إبداعية ومناسبة للمشروع\",\n            en: \"Required field: Project name - Provide suggestions for creative and suitable project names\"\n        },\n        purpose: {\n            ar: \"المجال المطلوب: الغرض من المشروع - اشرح الهدف الرئيسي والقيمة المضافة\",\n            en: \"Required field: Project purpose - Explain the main goal and added value\"\n        },\n        targetUsers: {\n            ar: \"المجال المطلوب: المستخدمون المستهدفون - حدد الجمهور المستهدف بدقة\",\n            en: \"Required field: Target users - Define the target audience precisely\"\n        },\n        goals: {\n            ar: \"المجال المطلوب: الأهداف - حدد أهداف واضحة وقابلة للقياس\",\n            en: \"Required field: Goals - Define clear and measurable objectives\"\n        },\n        scope: {\n            ar: \"المجال المطلوب: نطاق المشروع - حدد حدود وإمكانيات المشروع\",\n            en: \"Required field: Project scope - Define project boundaries and capabilities\"\n        },\n        timeline: {\n            ar: \"المجال المطلوب: الجدول الزمني - اقترح خطة زمنية واقعية\",\n            en: \"Required field: Timeline - Suggest a realistic time plan\"\n        },\n        programmingLanguages: {\n            ar: \"المجال المطلوب: لغات البرمجة - اقترح أفضل لغات البرمجة للمشروع\",\n            en: \"Required field: Programming languages - Suggest the best programming languages for the project\"\n        },\n        frameworks: {\n            ar: \"المجال المطلوب: الأطر التقنية - اقترح أفضل الأطر والمكتبات\",\n            en: \"Required field: Frameworks - Suggest the best frameworks and libraries\"\n        },\n        databases: {\n            ar: \"المجال المطلوب: قواعد البيانات - اقترح أنسب قواعد البيانات\",\n            en: \"Required field: Databases - Suggest the most suitable databases\"\n        }\n    };\n    const guidance = fieldGuidanceMap[fieldName];\n    if (guidance) {\n        return isArabic ? guidance.ar : guidance.en;\n    }\n    return isArabic ? `المجال المطلوب: ${fieldName} - قدم محتوى مفيد ومناسب لهذا المجال` : `Required field: ${fieldName} - Provide helpful and appropriate content for this field`;\n}\nasync function generateOpenAICompatible(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // إضافة timeout للطلب\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000); // 30 ثانية timeout\n        const response = await fetch(`${baseUrl}/chat/completions`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"Authorization\": `Bearer ${apiKey}`\n            },\n            body: JSON.stringify({\n                model,\n                messages,\n                temperature,\n                max_tokens: Math.min(maxTokens, 300),\n                stream: false // تأكد من عدم استخدام streaming\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.choices?.[0]?.message?.content || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usage\n        };\n    } catch (error) {\n        if (error instanceof Error && error.name === \"AbortError\") {\n            return {\n                success: false,\n                error: \"Request timeout - please try again\"\n            };\n        }\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateAnthropic(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // Convert OpenAI format to Anthropic format\n        const anthropicMessages = messages.filter((m)=>m.role !== \"system\");\n        const systemMessage = messages.find((m)=>m.role === \"system\")?.content || \"\";\n        const response = await fetch(`${baseUrl}/messages`, {\n            method: \"POST\",\n            headers: {\n                ...headers,\n                \"x-api-key\": apiKey\n            },\n            body: JSON.stringify({\n                model,\n                max_tokens: maxTokens,\n                temperature,\n                system: systemMessage,\n                messages: anthropicMessages\n            })\n        });\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.content?.[0]?.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usage\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\nasync function generateGoogle(baseUrl, apiKey, headers, model, messages, temperature, maxTokens) {\n    try {\n        // إضافة timeout\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 30000);\n        // Convert to Google format\n        const contents = messages.map((msg)=>({\n                role: msg.role === \"assistant\" ? \"model\" : \"user\",\n                parts: [\n                    {\n                        text: msg.content\n                    }\n                ]\n            }));\n        const response = await fetch(`${baseUrl}/models/${model}:generateContent?key=${apiKey}`, {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify({\n                contents,\n                generationConfig: {\n                    temperature,\n                    maxOutputTokens: Math.min(maxTokens, 300),\n                    candidateCount: 1 // طلب مرشح واحد فقط للسرعة\n                }\n            }),\n            signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n            const error = await response.text();\n            throw new Error(`API error: ${response.status} - ${error}`);\n        }\n        const data = await response.json();\n        const content = data.candidates?.[0]?.content?.parts?.[0]?.text || \"\";\n        return {\n            success: true,\n            content,\n            usage: data.usageMetadata\n        };\n    } catch (error) {\n        if (error instanceof Error && error.name === \"AbortError\") {\n            return {\n                success: false,\n                error: \"Request timeout - please try again\"\n            };\n        }\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/llm/generate/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/llmProviders.ts":
/*!*********************************!*\
  !*** ./src/lib/llmProviders.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLM_PROVIDERS_DATABASE: () => (/* binding */ LLM_PROVIDERS_DATABASE),\n/* harmony export */   getActiveProviders: () => (/* binding */ getActiveProviders),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProviderBaseUrl: () => (/* binding */ getProviderBaseUrl),\n/* harmony export */   getProviderById: () => (/* binding */ getProviderById),\n/* harmony export */   getProviderHeaders: () => (/* binding */ getProviderHeaders),\n/* harmony export */   searchProviders: () => (/* binding */ searchProviders)\n/* harmony export */ });\n/**\n * قاعدة بيانات شاملة لمقدمي خدمات LLM\n * تحتوي على معلومات كاملة عن كل مقدم خدمة مع Base URLs والنماذج\n */ const LLM_PROVIDERS_DATABASE = [\n    {\n        id: \"openai\",\n        name: \"OpenAI\",\n        icon: \"\\uD83E\\uDD16\",\n        description: \"GPT models from OpenAI - Industry leading language models\",\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"gpt-4o\",\n                name: \"GPT-4o\",\n                description: \"Most advanced multimodal model\",\n                contextLength: 128000,\n                pricing: \"$5/1M input, $15/1M output\",\n                inputPrice: 5,\n                outputPrice: 15\n            },\n            {\n                id: \"gpt-4o-mini\",\n                name: \"GPT-4o Mini\",\n                description: \"Faster and more affordable\",\n                contextLength: 128000,\n                pricing: \"$0.15/1M input, $0.6/1M output\",\n                inputPrice: 0.15,\n                outputPrice: 0.6\n            },\n            {\n                id: \"gpt-4-turbo\",\n                name: \"GPT-4 Turbo\",\n                description: \"High performance model\",\n                contextLength: 128000,\n                pricing: \"$10/1M input, $30/1M output\",\n                inputPrice: 10,\n                outputPrice: 30\n            },\n            {\n                id: \"gpt-3.5-turbo\",\n                name: \"GPT-3.5 Turbo\",\n                description: \"Fast and efficient\",\n                contextLength: 16385,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            }\n        ]\n    },\n    {\n        id: \"anthropic\",\n        name: \"Anthropic\",\n        icon: \"\\uD83E\\uDDE0\",\n        description: \"Claude models from Anthropic - Advanced reasoning capabilities\",\n        baseUrl: \"https://api.anthropic.com/v1\",\n        apiKeyPlaceholder: \"sk-ant-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"anthropic-version\": \"2023-06-01\"\n        },\n        models: [\n            {\n                id: \"claude-3-5-sonnet-20241022\",\n                name: \"Claude 3.5 Sonnet\",\n                description: \"Most intelligent model\",\n                contextLength: 200000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"claude-3-5-haiku-20241022\",\n                name: \"Claude 3.5 Haiku\",\n                description: \"Fastest model\",\n                contextLength: 200000,\n                pricing: \"$0.25/1M input, $1.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 1.25\n            },\n            {\n                id: \"claude-3-opus-20240229\",\n                name: \"Claude 3 Opus\",\n                description: \"Most powerful model\",\n                contextLength: 200000,\n                pricing: \"$15/1M input, $75/1M output\",\n                inputPrice: 15,\n                outputPrice: 75\n            }\n        ]\n    },\n    {\n        id: \"google\",\n        name: \"Google AI\",\n        icon: \"\\uD83D\\uDD0D\",\n        description: \"Gemini models from Google - Multimodal AI capabilities\",\n        baseUrl: \"https://generativelanguage.googleapis.com/v1beta\",\n        apiKeyPlaceholder: \"AIza...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 2000000,\n        models: [\n            {\n                id: \"gemini-1.5-pro\",\n                name: \"Gemini 1.5 Pro\",\n                description: \"Advanced reasoning with 2M context\",\n                contextLength: 2000000,\n                pricing: \"$1.25/1M input, $5/1M output\",\n                inputPrice: 1.25,\n                outputPrice: 5\n            },\n            {\n                id: \"gemini-1.5-flash\",\n                name: \"Gemini 1.5 Flash\",\n                description: \"Fast and efficient\",\n                contextLength: 1000000,\n                pricing: \"$0.075/1M input, $0.3/1M output\",\n                inputPrice: 0.075,\n                outputPrice: 0.3\n            },\n            {\n                id: \"gemini-pro\",\n                name: \"Gemini Pro\",\n                description: \"Balanced performance\",\n                contextLength: 32768,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            }\n        ]\n    },\n    {\n        id: \"openrouter\",\n        name: \"OpenRouter\",\n        icon: \"\\uD83D\\uDD00\",\n        description: \"Access to multiple models via OpenRouter - One API for all models\",\n        baseUrl: \"https://openrouter.ai/api/v1\",\n        apiKeyPlaceholder: \"sk-or-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n            \"X-Title\": \"ContextKit\"\n        },\n        models: [\n            {\n                id: \"openai/gpt-4o\",\n                name: \"GPT-4o (via OpenRouter)\",\n                description: \"OpenAI GPT-4o through OpenRouter\",\n                contextLength: 128000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"anthropic/claude-3.5-sonnet\",\n                name: \"Claude 3.5 Sonnet (via OpenRouter)\",\n                description: \"Anthropic Claude through OpenRouter\",\n                contextLength: 200000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"google/gemini-pro-1.5\",\n                name: \"Gemini Pro 1.5 (via OpenRouter)\",\n                description: \"Google Gemini through OpenRouter\",\n                contextLength: 1000000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"meta-llama/llama-3.1-405b-instruct\",\n                name: \"Llama 3.1 405B (via OpenRouter)\",\n                description: \"Meta Llama through OpenRouter\",\n                contextLength: 131072,\n                pricing: \"Variable pricing\"\n            }\n        ]\n    },\n    {\n        id: \"deepseek\",\n        name: \"DeepSeek\",\n        icon: \"\\uD83C\\uDF0A\",\n        description: \"DeepSeek models - Efficient and cost-effective AI\",\n        baseUrl: \"https://api.deepseek.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"deepseek-chat\",\n                name: \"DeepSeek Chat\",\n                description: \"General purpose conversational AI\",\n                contextLength: 32768,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            },\n            {\n                id: \"deepseek-coder\",\n                name: \"DeepSeek Coder\",\n                description: \"Specialized for code generation\",\n                contextLength: 16384,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            }\n        ]\n    },\n    {\n        id: \"groq\",\n        name: \"Groq\",\n        icon: \"⚡\",\n        description: \"Groq - Ultra-fast inference with GroqChip technology\",\n        baseUrl: \"https://api.groq.com/openai/v1\",\n        apiKeyPlaceholder: \"gsk_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"llama-3.1-70b-versatile\",\n                name: \"Llama 3.1 70B\",\n                description: \"Meta Llama 3.1 70B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.59/1M input, $0.79/1M output\",\n                inputPrice: 0.59,\n                outputPrice: 0.79\n            },\n            {\n                id: \"llama-3.1-8b-instant\",\n                name: \"Llama 3.1 8B\",\n                description: \"Meta Llama 3.1 8B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.05/1M input, $0.08/1M output\",\n                inputPrice: 0.05,\n                outputPrice: 0.08\n            },\n            {\n                id: \"mixtral-8x7b-32768\",\n                name: \"Mixtral 8x7B\",\n                description: \"Mistral Mixtral 8x7B on Groq\",\n                contextLength: 32768,\n                pricing: \"$0.24/1M input, $0.24/1M output\",\n                inputPrice: 0.24,\n                outputPrice: 0.24\n            }\n        ]\n    }\n];\n/**\n * الحصول على مقدم خدمة بواسطة ID\n */ function getProviderById(id) {\n    return LLM_PROVIDERS_DATABASE.find((provider)=>provider.id === id);\n}\n/**\n * الحصول على جميع مقدمي الخدمة النشطين\n */ function getActiveProviders() {\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.isActive);\n}\n/**\n * الحصول على نموذج بواسطة provider ID و model ID\n */ function getModelById(providerId, modelId) {\n    const provider = getProviderById(providerId);\n    return provider?.models.find((model)=>model.id === modelId);\n}\n/**\n * البحث عن مقدمي الخدمة\n */ function searchProviders(query) {\n    const lowercaseQuery = query.toLowerCase();\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.name.toLowerCase().includes(lowercaseQuery) || provider.description.toLowerCase().includes(lowercaseQuery) || provider.models.some((model)=>model.name.toLowerCase().includes(lowercaseQuery) || model.description.toLowerCase().includes(lowercaseQuery)));\n}\n/**\n * تحديد Base URL التلقائي لمقدم الخدمة\n */ function getProviderBaseUrl(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.baseUrl || \"\";\n}\n/**\n * الحصول على Headers المطلوبة لمقدم الخدمة\n */ function getProviderHeaders(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.headers || {};\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/llmProviders.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fllm%2Fgenerate%2Froute&page=%2Fapi%2Fllm%2Fgenerate%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();