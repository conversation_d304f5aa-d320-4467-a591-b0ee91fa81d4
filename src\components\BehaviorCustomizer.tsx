'use client';

import { useState, useEffect } from 'react';
import { useContextStore } from '@/store/contextStore';
import { 
  Settings, 
  MessageSquare, 
  User, 
  Brain, 
  Clock,
  Target,
  BookOpen,
  Save,
  RotateCcw,
  Zap
} from 'lucide-react';

interface BehaviorSettings {
  responseLength: 'brief' | 'medium' | 'detailed';
  writingStyle: 'formal' | 'friendly' | 'technical' | 'creative';
  responseSpeed: 'fast' | 'balanced' | 'thorough';
  contextAwareness: 'low' | 'medium' | 'high';
  creativity: number; // 0-100
  accuracy: number; // 0-100
  taskPreferences: {
    [key: string]: {
      responseLength: string;
      writingStyle: string;
      customInstructions: string;
    };
  };
}

interface BehaviorCustomizerProps {
  isArabic: boolean;
  onClose?: () => void;
}

export default function BehaviorCustomizer({ isArabic, onClose }: BehaviorCustomizerProps) {
  const { currentLanguage } = useContextStore();
  
  const [settings, setSettings] = useState<BehaviorSettings>({
    responseLength: 'medium',
    writingStyle: 'friendly',
    responseSpeed: 'balanced',
    contextAwareness: 'medium',
    creativity: 70,
    accuracy: 85,
    taskPreferences: {}
  });

  const [selectedTask, setSelectedTask] = useState<string>('general');
  const [customInstructions, setCustomInstructions] = useState('');

  const translations = {
    ar: {
      title: 'تخصيص السلوك',
      responseSettings: 'إعدادات الاستجابة',
      responseLength: 'طول الاستجابة',
      brief: 'موجز',
      medium: 'متوسط',
      detailed: 'مفصل',
      writingStyle: 'نمط الكتابة',
      formal: 'رسمي',
      friendly: 'ودود',
      technical: 'تقني',
      creative: 'إبداعي',
      responseSpeed: 'سرعة الاستجابة',
      fast: 'سريع',
      balanced: 'متوازن',
      thorough: 'شامل',
      contextAwareness: 'الوعي بالسياق',
      low: 'منخفض',
      high: 'عالي',
      aiPersonality: 'شخصية الذكاء الاصطناعي',
      creativity: 'الإبداع',
      accuracy: 'الدقة',
      taskSpecific: 'إعدادات خاصة بالمهام',
      selectTask: 'اختر نوع المهمة',
      general: 'عام',
      coding: 'البرمجة',
      writing: 'الكتابة',
      analysis: 'التحليل',
      research: 'البحث',
      customInstructions: 'تعليمات مخصصة',
      instructionsPlaceholder: 'أدخل تعليمات مخصصة لهذا النوع من المهام...',
      save: 'حفظ',
      reset: 'إعادة تعيين',
      apply: 'تطبيق',
      preview: 'معاينة التغييرات'
    },
    en: {
      title: 'Behavior Customizer',
      responseSettings: 'Response Settings',
      responseLength: 'Response Length',
      brief: 'Brief',
      medium: 'Medium',
      detailed: 'Detailed',
      writingStyle: 'Writing Style',
      formal: 'Formal',
      friendly: 'Friendly',
      technical: 'Technical',
      creative: 'Creative',
      responseSpeed: 'Response Speed',
      fast: 'Fast',
      balanced: 'Balanced',
      thorough: 'Thorough',
      contextAwareness: 'Context Awareness',
      low: 'Low',
      high: 'High',
      aiPersonality: 'AI Personality',
      creativity: 'Creativity',
      accuracy: 'Accuracy',
      taskSpecific: 'Task-Specific Settings',
      selectTask: 'Select Task Type',
      general: 'General',
      coding: 'Coding',
      writing: 'Writing',
      analysis: 'Analysis',
      research: 'Research',
      customInstructions: 'Custom Instructions',
      instructionsPlaceholder: 'Enter custom instructions for this task type...',
      save: 'Save',
      reset: 'Reset',
      apply: 'Apply',
      preview: 'Preview Changes'
    }
  };

  const t = translations[isArabic ? 'ar' : 'en'];

  const taskTypes = [
    { key: 'general', icon: MessageSquare, label: t.general },
    { key: 'coding', icon: Brain, label: t.coding },
    { key: 'writing', icon: BookOpen, label: t.writing },
    { key: 'analysis', icon: Target, label: t.analysis },
    { key: 'research', icon: Clock, label: t.research }
  ];

  // تحميل الإعدادات المحفوظة
  useEffect(() => {
    const savedSettings = localStorage.getItem('contextkit-behavior');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(parsed);
      } catch (error) {
        console.error('Error loading behavior settings:', error);
      }
    }
  }, []);

  // تحديث التعليمات المخصصة عند تغيير المهمة
  useEffect(() => {
    const taskPrefs = settings.taskPreferences[selectedTask];
    setCustomInstructions(taskPrefs?.customInstructions || '');
  }, [selectedTask, settings.taskPreferences]);

  const updateTaskPreference = (taskKey: string, updates: Partial<BehaviorSettings['taskPreferences'][string]>) => {
    setSettings(prev => ({
      ...prev,
      taskPreferences: {
        ...prev.taskPreferences,
        [taskKey]: {
          ...prev.taskPreferences[taskKey],
          ...updates
        }
      }
    }));
  };

  const saveTaskInstructions = () => {
    updateTaskPreference(selectedTask, {
      customInstructions,
      responseLength: settings.responseLength,
      writingStyle: settings.writingStyle
    });
  };

  const resetToDefaults = () => {
    setSettings({
      responseLength: 'medium',
      writingStyle: 'friendly',
      responseSpeed: 'balanced',
      contextAwareness: 'medium',
      creativity: 70,
      accuracy: 85,
      taskPreferences: {}
    });
    setCustomInstructions('');
  };

  const saveSettings = () => {
    localStorage.setItem('contextkit-behavior', JSON.stringify(settings));
    onClose?.();
  };

  return (
    <div className={`max-w-4xl mx-auto p-6 bg-white dark:bg-gray-900 rounded-lg shadow-xl ${isArabic ? 'rtl' : 'ltr'}`} dir={isArabic ? 'rtl' : 'ltr'}>
      {/* العنوان */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
          <Settings className="w-6 h-6" />
          {t.title}
        </h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ×
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* الإعدادات العامة */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            {t.responseSettings}
          </h3>

          {/* طول الاستجابة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              {t.responseLength}
            </label>
            <div className="grid grid-cols-3 gap-2">
              {(['brief', 'medium', 'detailed'] as const).map((length) => (
                <button
                  key={length}
                  onClick={() => setSettings(prev => ({ ...prev, responseLength: length }))}
                  className={`p-3 rounded-lg border transition-colors text-center ${
                    settings.responseLength === length
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                      : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                >
                  {t[length]}
                </button>
              ))}
            </div>
          </div>

          {/* نمط الكتابة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              {t.writingStyle}
            </label>
            <div className="grid grid-cols-2 gap-2">
              {(['formal', 'friendly', 'technical', 'creative'] as const).map((style) => (
                <button
                  key={style}
                  onClick={() => setSettings(prev => ({ ...prev, writingStyle: style }))}
                  className={`p-3 rounded-lg border transition-colors text-center ${
                    settings.writingStyle === style
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                      : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                >
                  {t[style]}
                </button>
              ))}
            </div>
          </div>

          {/* سرعة الاستجابة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              {t.responseSpeed}
            </label>
            <div className="grid grid-cols-3 gap-2">
              {(['fast', 'balanced', 'thorough'] as const).map((speed) => (
                <button
                  key={speed}
                  onClick={() => setSettings(prev => ({ ...prev, responseSpeed: speed }))}
                  className={`p-3 rounded-lg border transition-colors text-center ${
                    settings.responseSpeed === speed
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                      : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                >
                  {t[speed]}
                </button>
              ))}
            </div>
          </div>

          {/* شخصية الذكاء الاصطناعي */}
          <div>
            <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <User className="w-4 h-4" />
              {t.aiPersonality}
            </h4>
            
            <div className="space-y-4">
              {/* الإبداع */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t.creativity}
                  </label>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {settings.creativity}%
                  </span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.creativity}
                  onChange={(e) => setSettings(prev => ({ ...prev, creativity: parseInt(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              {/* الدقة */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t.accuracy}
                  </label>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {settings.accuracy}%
                  </span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.accuracy}
                  onChange={(e) => setSettings(prev => ({ ...prev, accuracy: parseInt(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
              </div>
            </div>
          </div>
        </div>

        {/* الإعدادات الخاصة بالمهام */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <Target className="w-5 h-5" />
            {t.taskSpecific}
          </h3>

          {/* اختيار نوع المهمة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              {t.selectTask}
            </label>
            <div className="grid grid-cols-2 gap-2">
              {taskTypes.map(({ key, icon: Icon, label }) => (
                <button
                  key={key}
                  onClick={() => setSelectedTask(key)}
                  className={`flex items-center gap-2 p-3 rounded-lg border transition-colors ${
                    selectedTask === key
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                      : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {label}
                </button>
              ))}
            </div>
          </div>

          {/* التعليمات المخصصة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t.customInstructions}
            </label>
            <textarea
              value={customInstructions}
              onChange={(e) => setCustomInstructions(e.target.value)}
              placeholder={t.instructionsPlaceholder}
              rows={6}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isArabic ? 'text-right' : 'text-left'}`}
              dir={isArabic ? 'rtl' : 'ltr'}
            />
            <button
              onClick={saveTaskInstructions}
              className="mt-2 flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Save className="w-4 h-4" />
              {t.save}
            </button>
          </div>

          {/* معاينة الإعدادات المحفوظة */}
          {Object.keys(settings.taskPreferences).length > 0 && (
            <div>
              <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                {t.preview}
              </h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {Object.entries(settings.taskPreferences).map(([taskKey, prefs]) => (
                  <div key={taskKey} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {taskTypes.find(t => t.key === taskKey)?.label || taskKey}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {prefs.responseLength} • {prefs.writingStyle}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* أزرار الإجراءات */}
      <div className="flex items-center justify-between pt-6 mt-8 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={resetToDefaults}
          className="flex items-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
          {t.reset}
        </button>
        
        <button
          onClick={saveSettings}
          className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Zap className="w-4 h-4" />
          {t.apply}
        </button>
      </div>
    </div>
  );
}
