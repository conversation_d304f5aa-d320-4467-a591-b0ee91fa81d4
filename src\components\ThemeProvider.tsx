'use client';

import { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('light');
  const [mounted, setMounted] = useState(false);

  // Initialize theme from localStorage only, ignore system preference after first load
  useEffect(() => {
    const savedTheme = localStorage.getItem('contextkit-theme') as Theme;

    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
      // إذا كان هناك theme محفوظ، استخدمه
      setTheme(savedTheme);
    } else {
      // فقط في المرة الأولى، استخدم system preference
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      setTheme(systemTheme);
      localStorage.setItem('contextkit-theme', systemTheme);
    }

    setMounted(true);
  }, []);

  // Apply theme to document immediately
  useEffect(() => {
    if (mounted) {
      const root = document.documentElement;

      // إزالة جميع classes المتعلقة بالـ theme أولاً
      root.classList.remove('light', 'dark');

      // إضافة الـ class الصحيح
      root.classList.add(theme);

      // حفظ في localStorage مع مفتاح مخصص
      localStorage.setItem('contextkit-theme', theme);

      // تطبيق فوري على body أيضاً
      document.body.setAttribute('data-theme', theme);
    }
  }, [theme, mounted]);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);

    // حفظ فوري في localStorage
    localStorage.setItem('contextkit-theme', newTheme);
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      <div suppressHydrationWarning>
        {children}
      </div>
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    // Return default values instead of throwing error during SSR
    return {
      theme: 'light' as Theme,
      toggleTheme: () => {}
    };
  }
  return context;
}
