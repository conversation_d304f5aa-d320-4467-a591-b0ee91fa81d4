'use client';

import { useState, useEffect } from 'react';
import { useContextStore } from '@/store/contextStore';
import Header from '@/components/Header';
import InteractiveAssistant from '@/components/InteractiveAssistant';
import CollapsibleSidebar from '@/components/CollapsibleSidebar';
import AppearanceCustomizer from '@/components/AppearanceCustomizer';
import BehaviorCustomizer from '@/components/BehaviorCustomizer';
import { 
  Bot, 
  Settings, 
  Palette, 
  Sliders,
  MessageSquare,
  Sparkles
} from 'lucide-react';

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  isStarred: boolean;
  messageCount: number;
}

export default function InteractivePage() {
  const { currentLanguage } = useContextStore();
  const isArabic = currentLanguage === 'ar';
  
  const [conversations, setConversations] = useState<Conversation[]>([
    {
      id: '1',
      title: isArabic ? 'مساعدة في البرمجة' : 'Programming Help',
      lastMessage: isArabic ? 'كيف يمكنني تحسين أداء React؟' : 'How can I improve React performance?',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      isStarred: true,
      messageCount: 12
    },
    {
      id: '2',
      title: isArabic ? 'تصميم واجهة المستخدم' : 'UI Design Discussion',
      lastMessage: isArabic ? 'ما هي أفضل الممارسات في UX؟' : 'What are the best UX practices?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isStarred: false,
      messageCount: 8
    },
    {
      id: '3',
      title: isArabic ? 'تحليل البيانات' : 'Data Analysis',
      lastMessage: isArabic ? 'كيف أحلل هذه البيانات؟' : 'How do I analyze this data?',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      isStarred: false,
      messageCount: 15
    }
  ]);

  const [currentConversationId, setCurrentConversationId] = useState<string>('1');
  const [showAppearanceCustomizer, setShowAppearanceCustomizer] = useState(false);
  const [showBehaviorCustomizer, setShowBehaviorCustomizer] = useState(false);

  const translations = {
    ar: {
      title: 'المساعد الذكي التفاعلي',
      subtitle: 'تجربة محادثة متقدمة مع الذكاء الاصطناعي',
      appearance: 'تخصيص المظهر',
      behavior: 'تخصيص السلوك',
      newConversation: 'محادثة جديدة',
      features: 'الميزات المتقدمة',
      feature1: 'دعم Markdown وتمييز الكود',
      feature2: 'تأثيرات كتابة في الوقت الفعلي',
      feature3: 'شريط جانبي قابل للطي',
      feature4: 'تخصيص كامل للمظهر والسلوك'
    },
    en: {
      title: 'Interactive AI Assistant',
      subtitle: 'Advanced conversational experience with artificial intelligence',
      appearance: 'Customize Appearance',
      behavior: 'Customize Behavior',
      newConversation: 'New Conversation',
      features: 'Advanced Features',
      feature1: 'Markdown support and code highlighting',
      feature2: 'Real-time typing effects',
      feature3: 'Collapsible sidebar',
      feature4: 'Full appearance and behavior customization'
    }
  };

  const t = translations[isArabic ? 'ar' : 'en'];

  const handleConversationSelect = (id: string) => {
    setCurrentConversationId(id);
  };

  const handleNewConversation = () => {
    const newId = Date.now().toString();
    const newConversation: Conversation = {
      id: newId,
      title: isArabic ? 'محادثة جديدة' : 'New Conversation',
      lastMessage: '',
      timestamp: new Date(),
      isStarred: false,
      messageCount: 0
    };
    setConversations(prev => [newConversation, ...prev]);
    setCurrentConversationId(newId);
  };

  const handleDeleteConversation = (id: string) => {
    setConversations(prev => prev.filter(conv => conv.id !== id));
    if (currentConversationId === id && conversations.length > 1) {
      const remainingConversations = conversations.filter(conv => conv.id !== id);
      setCurrentConversationId(remainingConversations[0]?.id || '');
    }
  };

  const handleToggleStarred = (id: string) => {
    setConversations(prev => prev.map(conv => 
      conv.id === id ? { ...conv, isStarred: !conv.isStarred } : conv
    ));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Header
        title={t.title}
        subtitle={t.subtitle}
        emoji="🤖"
      />

      {/* الشريط الجانبي */}
      <CollapsibleSidebar
        isArabic={isArabic}
        conversations={conversations}
        currentConversationId={currentConversationId}
        onConversationSelect={handleConversationSelect}
        onNewConversation={handleNewConversation}
        onDeleteConversation={handleDeleteConversation}
        onToggleStarred={handleToggleStarred}
      />

      {/* المحتوى الرئيسي */}
      <div className="container mx-auto px-4 py-8">
        {/* أزرار التخصيص */}
        <div className={`flex items-center gap-4 mb-6 ${isArabic ? 'justify-start' : 'justify-end'}`}>
          <button
            onClick={() => setShowAppearanceCustomizer(true)}
            className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-600"
          >
            <Palette className="w-4 h-4" />
            {t.appearance}
          </button>
          
          <button
            onClick={() => setShowBehaviorCustomizer(true)}
            className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-600"
          >
            <Sliders className="w-4 h-4" />
            {t.behavior}
          </button>
        </div>

        {/* المساعد التفاعلي */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden" style={{ height: '70vh' }}>
            <InteractiveAssistant isArabic={isArabic} />
          </div>
        </div>

        {/* الميزات المتقدمة */}
        <div className="max-w-4xl mx-auto mt-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 className={`text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2 ${isArabic ? 'text-right' : 'text-left'}`}>
              <Sparkles className="w-5 h-5" />
              {t.features}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className={`flex items-center gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg ${isArabic ? 'flex-row-reverse text-right' : ''}`}>
                <MessageSquare className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                <span className="text-gray-700 dark:text-gray-300">{t.feature1}</span>
              </div>
              <div className={`flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg ${isArabic ? 'flex-row-reverse text-right' : ''}`}>
                <Bot className="w-5 h-5 text-green-600 dark:text-green-400" />
                <span className="text-gray-700 dark:text-gray-300">{t.feature2}</span>
              </div>
              <div className={`flex items-center gap-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg ${isArabic ? 'flex-row-reverse text-right' : ''}`}>
                <Settings className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                <span className="text-gray-700 dark:text-gray-300">{t.feature3}</span>
              </div>
              <div className={`flex items-center gap-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg ${isArabic ? 'flex-row-reverse text-right' : ''}`}>
                <Palette className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                <span className="text-gray-700 dark:text-gray-300">{t.feature4}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* مودال تخصيص المظهر */}
      {showAppearanceCustomizer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <AppearanceCustomizer
              isArabic={isArabic}
              onClose={() => setShowAppearanceCustomizer(false)}
            />
          </div>
        </div>
      )}

      {/* مودال تخصيص السلوك */}
      {showBehaviorCustomizer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="max-w-5xl w-full max-h-[90vh] overflow-y-auto">
            <BehaviorCustomizer
              isArabic={isArabic}
              onClose={() => setShowBehaviorCustomizer(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
