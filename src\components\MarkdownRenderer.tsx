'use client';

import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useTheme } from '@/components/ThemeProvider';
import { Copy, Check } from 'lucide-react';
import { useState } from 'react';

interface MarkdownRendererProps {
  content: string;
  isArabic?: boolean;
  className?: string;
}

export default function MarkdownRenderer({ content, isArabic = false, className = '' }: MarkdownRendererProps) {
  const { theme } = useTheme();
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedCode(text);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div 
      className={`prose prose-gray dark:prose-invert max-w-none ${isArabic ? 'prose-rtl' : ''} ${className}`}
      dir={isArabic ? 'rtl' : 'ltr'}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // تخصيص عرض الكود
          code({ node, inline, className, children, ...props }) {
            const match = /language-(\w+)/.exec(className || '');
            const language = match ? match[1] : '';
            const codeString = String(children).replace(/\n$/, '');

            if (!inline && language) {
              return (
                <div className="relative group">
                  {/* زر النسخ */}
                  <button
                    onClick={() => copyToClipboard(codeString)}
                    className={`absolute top-2 ${isArabic ? 'left-2' : 'right-2'} z-10 p-2 bg-gray-800 dark:bg-gray-700 text-white rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-gray-700 dark:hover:bg-gray-600`}
                    title={isArabic ? 'نسخ الكود' : 'Copy code'}
                  >
                    {copiedCode === codeString ? (
                      <Check className="w-4 h-4 text-green-400" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </button>

                  {/* عرض الكود مع التمييز */}
                  <SyntaxHighlighter
                    style={theme === 'dark' ? oneDark : oneLight}
                    language={language}
                    PreTag="div"
                    customStyle={{
                      margin: 0,
                      borderRadius: '0.5rem',
                      fontSize: '0.875rem',
                      direction: 'ltr', // الكود دائماً من اليسار لليمين
                    }}
                    codeTagProps={{
                      style: {
                        fontFamily: 'Fira Code, Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace',
                      }
                    }}
                    {...props}
                  >
                    {codeString}
                  </SyntaxHighlighter>
                </div>
              );
            }

            // الكود المضمن
            return (
              <code 
                className="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-800 rounded text-sm font-mono"
                style={{ direction: 'ltr' }}
                {...props}
              >
                {children}
              </code>
            );
          },

          // تخصيص عرض الجداول
          table({ children }) {
            return (
              <div className="overflow-x-auto my-4">
                <table className="min-w-full border-collapse border border-gray-300 dark:border-gray-600">
                  {children}
                </table>
              </div>
            );
          },

          // تخصيص عرض رؤوس الجداول
          th({ children }) {
            return (
              <th className={`border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 px-4 py-2 font-semibold ${isArabic ? 'text-right' : 'text-left'}`}>
                {children}
              </th>
            );
          },

          // تخصيص عرض خلايا الجداول
          td({ children }) {
            return (
              <td className={`border border-gray-300 dark:border-gray-600 px-4 py-2 ${isArabic ? 'text-right' : 'text-left'}`}>
                {children}
              </td>
            );
          },

          // تخصيص عرض القوائم
          ul({ children }) {
            return (
              <ul className={`list-disc ${isArabic ? 'list-inside mr-4' : 'list-inside ml-4'} space-y-1`}>
                {children}
              </ul>
            );
          },

          ol({ children }) {
            return (
              <ol className={`list-decimal ${isArabic ? 'list-inside mr-4' : 'list-inside ml-4'} space-y-1`}>
                {children}
              </ol>
            );
          },

          // تخصيص عرض الاقتباسات
          blockquote({ children }) {
            return (
              <blockquote className={`border-l-4 border-blue-500 ${isArabic ? 'pr-4 mr-2' : 'pl-4 ml-2'} py-2 bg-blue-50 dark:bg-blue-900/20 italic text-gray-700 dark:text-gray-300`}>
                {children}
              </blockquote>
            );
          },

          // تخصيص عرض الروابط
          a({ href, children }) {
            return (
              <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline transition-colors"
              >
                {children}
              </a>
            );
          },

          // تخصيص عرض العناوين
          h1({ children }) {
            return (
              <h1 className={`text-2xl font-bold mb-4 mt-6 text-gray-900 dark:text-white ${isArabic ? 'text-right' : 'text-left'}`}>
                {children}
              </h1>
            );
          },

          h2({ children }) {
            return (
              <h2 className={`text-xl font-bold mb-3 mt-5 text-gray-900 dark:text-white ${isArabic ? 'text-right' : 'text-left'}`}>
                {children}
              </h2>
            );
          },

          h3({ children }) {
            return (
              <h3 className={`text-lg font-bold mb-2 mt-4 text-gray-900 dark:text-white ${isArabic ? 'text-right' : 'text-left'}`}>
                {children}
              </h3>
            );
          },

          // تخصيص عرض الفقرات
          p({ children }) {
            return (
              <p className={`mb-4 leading-relaxed text-gray-700 dark:text-gray-300 ${isArabic ? 'text-right' : 'text-left'}`}>
                {children}
              </p>
            );
          },

          // تخصيص عرض الخطوط الفاصلة
          hr() {
            return <hr className="my-6 border-gray-300 dark:border-gray-600" />;
          },

          // تخصيص عرض النص المميز
          strong({ children }) {
            return <strong className="font-bold text-gray-900 dark:text-white">{children}</strong>;
          },

          em({ children }) {
            return <em className="italic text-gray-700 dark:text-gray-300">{children}</em>;
          },
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
