'use client';

import { useState, useEffect, useRef } from 'react';
import { useContextStore } from '@/store/contextStore';
import {
  Send,
  Loader2,
  <PERSON><PERSON>,
  User,
  Co<PERSON>,
  Refresh<PERSON><PERSON>,
  <PERSON>rk<PERSON>,
  MessageSquare
} from 'lucide-react';
import MarkdownRenderer from './MarkdownRenderer';

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  isTyping?: boolean;
}

interface InteractiveAssistantProps {
  isArabic: boolean;
  onClose?: () => void;
}

export default function InteractiveAssistant({ isArabic, onClose }: InteractiveAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const { getActiveProviders } = useContextStore();

  const translations = {
    ar: {
      title: 'المساعد الذكي التفاعلي',
      placeholder: 'اكتب رسالتك هنا...',
      send: 'إرسال',
      copy: 'نسخ',
      regenerate: 'إعادة توليد',
      thinking: 'يفكر...',
      typing: 'يكتب...',
      noProviders: 'لا توجد مزودي خدمة نشطين',
      error: 'حدث خطأ أثناء المعالجة'
    },
    en: {
      title: 'Interactive AI Assistant',
      placeholder: 'Type your message here...',
      send: 'Send',
      copy: 'Copy',
      regenerate: 'Regenerate',
      thinking: 'Thinking...',
      typing: 'Typing...',
      noProviders: 'No active providers',
      error: 'An error occurred during processing'
    }
  };

  const t = translations[isArabic ? 'ar' : 'en'];

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const typewriterEffect = (text: string, messageId: string) => {
    setIsTyping(true);
    let index = 0;
    const interval = setInterval(() => {
      if (index < text.length) {
        setMessages(prev => prev.map(msg => 
          msg.id === messageId 
            ? { ...msg, content: text.substring(0, index + 1) }
            : msg
        ));
        index++;
      } else {
        setIsTyping(false);
        clearInterval(interval);
      }
    }, 30); // Typing speed

    return () => clearInterval(interval);
  };

  const sendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const activeProviders = getActiveProviders();
    if (activeProviders.length === 0) {
      alert(t.noProviders);
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      role: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Add assistant message placeholder
    const assistantMessageId = (Date.now() + 1).toString();
    const assistantMessage: Message = {
      id: assistantMessageId,
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isTyping: true
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      // Simulate API call
      const response = await fetch('/api/llm/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: inputValue,
          context: {},
          fieldName: 'general',
          language: isArabic ? 'ar' : 'en'
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate response');
      }

      const data = await response.json();
      
      if (data.success) {
        // Start typewriter effect
        typewriterEffect(data.content, assistantMessageId);
      } else {
        throw new Error(data.error || t.error);
      }
    } catch (error) {
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessageId 
          ? { ...msg, content: t.error, isTyping: false }
          : msg
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const regenerateMessage = (messageId: string) => {
    // Find the user message before this assistant message
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex > 0) {
      const userMessage = messages[messageIndex - 1];
      if (userMessage.role === 'user') {
        setInputValue(userMessage.content);
        // Remove the assistant message and regenerate
        setMessages(prev => prev.filter(msg => msg.id !== messageId));
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className={`flex flex-col h-full bg-white dark:bg-gray-900 rounded-lg shadow-xl ${isArabic ? 'rtl' : 'ltr'}`} dir={isArabic ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <Bot className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">{t.title}</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {isTyping ? t.typing : isLoading ? t.thinking : 'Online'}
            </p>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ×
          </button>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center text-gray-500 dark:text-gray-400 py-8">
            <Sparkles className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>{isArabic ? 'ابدأ محادثة مع المساعد الذكي' : 'Start a conversation with the AI assistant'}</p>
          </div>
        )}

        {messages.map((message) => (
          <MessageBubble
            key={message.id}
            message={message}
            isArabic={isArabic}
            onCopy={copyMessage}
            onRegenerate={regenerateMessage}
            translations={t}
          />
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={t.placeholder}
            className={`flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white ${isArabic ? 'text-right' : 'text-left'}`}
            disabled={isLoading}
            dir={isArabic ? 'rtl' : 'ltr'}
          />
          <button
            onClick={sendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

interface MessageBubbleProps {
  message: Message;
  isArabic: boolean;
  onCopy: (content: string) => void;
  onRegenerate: (messageId: string) => void;
  translations: any;
}

function MessageBubble({ message, isArabic, onCopy, onRegenerate, translations }: MessageBubbleProps) {
  const isUser = message.role === 'user';
  
  return (
    <div className={`flex ${isUser ? (isArabic ? 'justify-start' : 'justify-end') : (isArabic ? 'justify-end' : 'justify-start')} group`}>
      <div className={`flex items-start gap-3 max-w-[80%] ${isUser && isArabic ? 'flex-row' : isUser ? 'flex-row-reverse' : isArabic ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
          isUser 
            ? 'bg-gradient-to-br from-green-500 to-emerald-600' 
            : 'bg-gradient-to-br from-blue-500 to-purple-600'
        }`}>
          {isUser ? (
            <User className="w-4 h-4 text-white" />
          ) : (
            <Bot className="w-4 h-4 text-white" />
          )}
        </div>

        {/* Message Content */}
        <div className={`relative px-4 py-3 rounded-2xl ${
          isUser
            ? 'bg-blue-600 text-white'
            : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
        } ${isArabic ? 'text-right' : 'text-left'}`}>
          {isUser ? (
            <p className="whitespace-pre-wrap break-words">
              {message.content}
            </p>
          ) : (
            <div className="prose-sm">
              <MarkdownRenderer
                content={message.content}
                isArabic={isArabic}
                className="text-inherit"
              />
              {message.isTyping && (
                <span className="inline-block w-2 h-5 bg-current opacity-75 animate-pulse ml-1" />
              )}
            </div>
          )}

          {/* Message Actions */}
          {!isUser && message.content && (
            <div className={`absolute top-2 ${isArabic ? 'left-2' : 'right-2'} opacity-0 group-hover:opacity-100 transition-opacity flex gap-1`}>
              <button
                onClick={() => onCopy(message.content)}
                className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                title={translations.copy}
              >
                <Copy className="w-3 h-3" />
              </button>
              <button
                onClick={() => onRegenerate(message.id)}
                className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                title={translations.regenerate}
              >
                <RefreshCw className="w-3 h-3" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
