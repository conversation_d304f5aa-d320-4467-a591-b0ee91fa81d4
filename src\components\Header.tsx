'use client';

import Link from 'next/link';
import { Home, Settings } from 'lucide-react';
import ThemeToggle from './ThemeToggle';
import LanguageToggle from './LanguageToggle';
import ClearContentButton from './ClearContentButton';
import { useContextStore } from '@/store/contextStore';

interface HeaderProps {
  title: string;
  subtitle?: string;
  backLink?: {
    href: string;
    label: string;
  };
  emoji?: string;
}

export default function Header({ title, subtitle, backLink, emoji }: HeaderProps) {
  const { currentLanguage } = useContextStore();
  const isArabic = currentLanguage === 'ar';

  return (
    <header className="relative">
      {/* Controls - Fixed position in top right */}
      <div className={`fixed top-4 ${isArabic ? 'left-4' : 'right-4'} z-50 flex gap-3`}>
        <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2">
          <div className="flex gap-2">
            <Link
              href="/"
              className="relative w-12 h-12 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden"
              title={isArabic ? 'الصفحة الرئيسية' : 'Home'}
            >
              <div className="absolute inset-0 flex items-center justify-center">
                <Home className="w-6 h-6 text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors" />
              </div>

              {/* Subtle glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </Link>
            <Link
              href="/settings"
              className="relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden"
              title={isArabic ? 'إعدادات API' : 'API Settings'}
            >
              <div className="absolute inset-0 flex items-center justify-center">
                <Settings className="w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90" />
              </div>

              {/* Subtle glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </Link>
            <ClearContentButton />
            <LanguageToggle />
            <ThemeToggle />
          </div>
        </div>
      </div>

      {/* Header Content */}
      <div className="text-center mb-8 pt-4" dir={isArabic ? 'rtl' : 'ltr'}>
        {backLink && (
          <Link
            href={backLink.href}
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors"
          >
            {backLink.label}
          </Link>
        )}
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2">
          {emoji && <span className={isArabic ? 'ml-2' : 'mr-2'}>{emoji}</span>}
          {title}
        </h1>
        {subtitle && (
          <p className="text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {subtitle}
          </p>
        )}
      </div>
    </header>
  );
}
