'use client';

import { useContextStore } from '@/store/contextStore';
import { Trash2 } from 'lucide-react';

export default function ClearContentButton() {
  const { currentLanguage, clearAllAnswers } = useContextStore();
  const isArabic = currentLanguage === 'ar';

  const translations = {
    ar: {
      clearAll: 'مسح جميع الإجابات',
      confirmMessage: 'هل أنت متأكد من مسح جميع الإجابات؟ لا يمكن التراجع عن هذا الإجراء.',
      success: 'تم مسح جميع الإجابات بنجاح'
    },
    en: {
      clearAll: 'Clear All Answers',
      confirmMessage: 'Are you sure you want to clear all answers? This action cannot be undone.',
      success: 'All answers cleared successfully'
    }
  };

  const t = translations[isArabic ? 'ar' : 'en'];

  const handleClearAll = () => {
    if (window.confirm(t.confirmMessage)) {
      clearAllAnswers();

      // Show success message
      setTimeout(() => {
        alert(t.success);
      }, 100);
    }
  };

  return (
    <button
      onClick={handleClearAll}
      className="relative w-12 h-12 rounded-xl bg-gradient-to-br from-red-50 to-red-100 dark:from-gray-800 dark:to-gray-900 hover:from-red-100 hover:to-red-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden"
      title={t.clearAll}
      aria-label={t.clearAll}
    >
      <div className="absolute inset-0 flex items-center justify-center">
        <Trash2 className="w-6 h-6 text-red-500 dark:text-red-400 group-hover:text-red-600 dark:group-hover:text-red-300 transition-colors duration-300" />
      </div>

      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
    </button>
  );
}
