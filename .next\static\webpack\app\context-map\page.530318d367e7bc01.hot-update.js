"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/context-map/page",{

/***/ "(app-pages-browser)/./src/components/SmartQuestion.tsx":
/*!******************************************!*\
  !*** ./src/components/SmartQuestion.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartQuestion; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _SmartFieldAssistant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SmartFieldAssistant */ \"(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartQuestion(param) {\n    let { id, question, questionAr, placeholder, placeholderAr, value, onChange, type = \"textarea\", aiSuggestion, aiSuggestionAr, promptTemplate } = param;\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    const handleCopy = async ()=>{\n        if (value.trim()) {\n            await navigator.clipboard.writeText(value);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handlePromptCopy = async ()=>{\n        if (promptTemplate && value.trim()) {\n            const prompt = promptTemplate.replace(\"{answer}\", value);\n            await navigator.clipboard.writeText(prompt);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handleSuggestionApply = ()=>{\n        if (aiSuggestion || aiSuggestionAr) {\n            const suggestion = isArabic ? aiSuggestionAr : aiSuggestion;\n            if (suggestion && !value.trim()) {\n                onChange(suggestion);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg \".concat(isArabic ? \"rtl\" : \"ltr\"),\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white mb-2 \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                            children: isArabic ? questionAr : question\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowSuggestion(!showSuggestion),\n                            className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: isArabic ? \"ml-1\" : \"mr-1\",\n                                    children: \"\\uD83E\\uDDE0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                isArabic ? \"عرض الاقتراح الذكي\" : \"Show AI Suggestion\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            showSuggestion && (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-500 mr-2\",\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-blue-800 dark:text-blue-200\",\n                            children: isArabic ? aiSuggestionAr : aiSuggestion\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this),\n            type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-black dark:text-white resize-none font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                rows: 4,\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                style: {\n                    fontFamily: isArabic ? \"'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif\" : \"inherit\",\n                    lineHeight: isArabic ? \"1.8\" : \"1.5\",\n                    letterSpacing: isArabic ? \"0.02em\" : \"normal\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-black dark:text-white font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                style: {\n                    fontFamily: isArabic ? \"'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif\" : \"inherit\",\n                    lineHeight: isArabic ? \"1.8\" : \"1.5\",\n                    letterSpacing: isArabic ? \"0.02em\" : \"normal\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-2 pt-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartFieldAssistant__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        fieldName: id,\n                        fieldValue: value,\n                        onValueChange: onChange,\n                        placeholder: isArabic ? placeholderAr : placeholder,\n                        className: \"flex-shrink-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-gray-500/80 to-slate-600/80 hover:from-gray-600/90 hover:to-slate-700/90 text-white shadow-md hover:shadow-lg hover:shadow-gray-500/25 hover:scale-105 active:scale-95\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: copied ? \"animate-bounce\" : \"group-hover:scale-110 transition-transform duration-300\",\n                                        children: \"\\uD83D\\uDCCE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ\" : \"Copy\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            promptTemplate && value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handlePromptCopy,\n                    className: \"flex items-center px-4 py-2 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-1\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        isArabic ? \"نسخ كـ Prompt\" : \"Copy as Prompt\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartQuestion, \"23gELrRwHOSGqDEIjyy5y28Fmsk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartQuestion;\nvar _c;\n$RefreshReg$(_c, \"SmartQuestion\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartQuestion.tsx\n"));

/***/ })

});