"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/context-map/page",{

/***/ "(app-pages-browser)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartFieldAssistant; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartFieldAssistant(param) {\n    let { fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" } = param;\n    _s();\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeProviders, setActiveProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تجنب مشاكل الهيدريشن\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setActiveProviders(getActiveProviders());\n    }, [\n        getActiveProviders\n    ]);\n    // تحقق من وجود مقدم خدمة صالح\n    const hasValidProvider = mounted && activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً\" : \"Please configure an AI provider and select models in Settings first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n        fastGeneration: isArabic ? \"توليد سريع (محسّن)\" : \"Fast Generation (Optimized)\",\n        timeout: isArabic ? \"انتهت مهلة الطلب - حاول مرة أخرى\" : \"Request timeout - try again\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider === null || provider === void 0 ? void 0 : provider.name, \"with model:\", provider === null || provider === void 0 ? void 0 : provider.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.7,\n                maxTokens: 200 // تقليل maxTokens بشكل كبير للسرعة\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            // إضافة timeout للطلب من جانب العميل\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>{\n                controller.abort();\n            }, 35000); // 35 ثانية timeout\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody),\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // استخدام أول اقتراح مباشرة في خانة الكتابة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                if (suggestions.length > 0) {\n                    // وضع أول اقتراح في خانة الكتابة\n                    onValueChange(suggestions[0]);\n                    // حفظ باقي الاقتراحات للاستخدام لاحقاً\n                    setGeneratedSuggestions(suggestions);\n                } else {\n                    const errorMsg = isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\";\n                    onValueChange(errorMsg);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            onValueChange(\"\".concat(translations.error, \": \").concat(errorMessage));\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        const fieldPrompts = {\n            name: {\n                ar: \"اقترح 3 أسماء مناسبة للمشروع\",\n                en: \"Suggest 3 suitable project names\"\n            },\n            purpose: {\n                ar: \"اكتب 3 أوصاف مختلفة لغرض المشروع\",\n                en: \"Write 3 different project purpose descriptions\"\n            },\n            targetUsers: {\n                ar: \"حدد 3 مجموعات من المستخدمين المستهدفين\",\n                en: \"Define 3 target user groups\"\n            },\n            goals: {\n                ar: \"اقترح 3 أهداف للمشروع\",\n                en: \"Suggest 3 project goals\"\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? \"اقترح محتوى لـ \".concat(fieldName) : \"Suggest content for \".concat(fieldName);\n        // تبسيط السياق - أخذ المعلومات المهمة فقط\n        const relevantInfo = (context === null || context === void 0 ? void 0 : context.name) || (context === null || context === void 0 ? void 0 : context.purpose) || \"\";\n        const contextInfo = relevantInfo ? isArabic ? \"المشروع: \".concat(relevantInfo.substring(0, 50)) : \"Project: \".concat(relevantInfo.substring(0, 50)) : \"\";\n        const instructions = isArabic ? \"قدم 3 اقتراحات مرقمة، كل اقتراح في سطر منفصل.\" : \"Provide 3 numbered suggestions, each on a separate line.\";\n        return \"\".concat(contextInfo, \"\\n\").concat(basePrompt, \"\\n\").concat(instructions);\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const regenerateContent = async ()=>{\n        if (generatedSuggestions.length > 1) {\n            // استخدام الاقتراح التالي إذا كان متوفراً\n            const currentIndex = generatedSuggestions.findIndex((s)=>s === fieldValue);\n            const nextIndex = (currentIndex + 1) % generatedSuggestions.length;\n            onValueChange(generatedSuggestions[nextIndex]);\n        } else {\n            // توليد محتوى جديد\n            await generateSuggestions();\n        }\n    };\n    // تجنب مشاكل الهيدريشن\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                disabled: true,\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 cursor-not-allowed font-arabic\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this),\n                    isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 246,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group \".concat(hasValidProvider ? \"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95\" : \"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30\", \" \").concat(isGenerating ? \"animate-pulse scale-105\" : \"\"),\n                title: hasValidProvider ? translations.generateWithAI : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-2\",\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.generateWithAI\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            fieldValue && generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: regenerateContent,\n                className: \"relative flex items-center gap-2 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-orange-500/80 to-red-500/80 hover:from-orange-600/90 hover:to-red-600/90 text-white shadow-lg hover:shadow-xl hover:shadow-orange-500/25 hover:scale-105 active:scale-95\",\n                title: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 287,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 258,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartFieldAssistant, \"ZrnkxJH8z8I5/fadLS+VIi/I8fM=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartFieldAssistant;\nvar _c;\n$RefreshReg$(_c, \"SmartFieldAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\n"));

/***/ })

});