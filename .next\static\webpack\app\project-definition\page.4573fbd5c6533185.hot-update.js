"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/project-definition/page",{

/***/ "(app-pages-browser)/./src/components/SmartQuestion.tsx":
/*!******************************************!*\
  !*** ./src/components/SmartQuestion.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartQuestion; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _SmartFieldAssistant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SmartFieldAssistant */ \"(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartQuestion(param) {\n    let { id, question, questionAr, placeholder, placeholderAr, value, onChange, type = \"textarea\", aiSuggestion, aiSuggestionAr, promptTemplate } = param;\n    _s();\n    const { currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [showSuggestion, setShowSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    const handleCopy = async ()=>{\n        if (value.trim()) {\n            await navigator.clipboard.writeText(value);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handlePromptCopy = async ()=>{\n        if (promptTemplate && value.trim()) {\n            const prompt = promptTemplate.replace(\"{answer}\", value);\n            await navigator.clipboard.writeText(prompt);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        }\n    };\n    const handleSuggestionApply = ()=>{\n        if (aiSuggestion || aiSuggestionAr) {\n            const suggestion = isArabic ? aiSuggestionAr : aiSuggestion;\n            if (suggestion && !value.trim()) {\n                onChange(suggestion);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg \".concat(isArabic ? \"rtl\" : \"ltr\"),\n        dir: isArabic ? \"rtl\" : \"ltr\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white mb-2 \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                            children: isArabic ? questionAr : question\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowSuggestion(!showSuggestion),\n                            className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: isArabic ? \"ml-1\" : \"mr-1\",\n                                    children: \"\\uD83E\\uDDE0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                isArabic ? \"عرض الاقتراح الذكي\" : \"Show AI Suggestion\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            showSuggestion && (aiSuggestion || aiSuggestionAr) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start \".concat(isArabic ? \"flex-row-reverse\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-500 \".concat(isArabic ? \"ml-2\" : \"mr-2\"),\n                            children: \"\\uD83D\\uDCA1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-blue-800 dark:text-blue-200 \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                            children: isArabic ? aiSuggestionAr : aiSuggestion\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this),\n            type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-black dark:text-white resize-none font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                rows: 4,\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                style: {\n                    fontFamily: isArabic ? \"'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif\" : \"inherit\",\n                    lineHeight: isArabic ? \"1.8\" : \"1.5\",\n                    letterSpacing: isArabic ? \"0.02em\" : \"normal\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                value: value,\n                onChange: (e)=>onChange(e.target.value),\n                placeholder: isArabic ? placeholderAr : placeholder,\n                className: \"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-black dark:text-white font-arabic \".concat(isArabic ? \"text-right\" : \"text-left\"),\n                dir: isArabic ? \"rtl\" : \"ltr\",\n                style: {\n                    fontFamily: isArabic ? \"'Tajawal', 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif\" : \"inherit\",\n                    lineHeight: isArabic ? \"1.8\" : \"1.5\",\n                    letterSpacing: isArabic ? \"0.02em\" : \"normal\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between gap-2 pt-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SmartFieldAssistant__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        fieldName: id,\n                        fieldValue: value,\n                        onValueChange: onChange,\n                        placeholder: isArabic ? placeholderAr : placeholder,\n                        className: \"flex-shrink-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleCopy,\n                        className: \"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-gray-500/80 to-slate-600/80 hover:from-gray-600/90 hover:to-slate-700/90 text-white shadow-md hover:shadow-lg hover:shadow-gray-500/25 hover:scale-105 active:scale-95\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: copied ? \"animate-bounce\" : \"group-hover:scale-110 transition-transform duration-300\",\n                                        children: \"\\uD83D\\uDCCE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    copied ? isArabic ? \"تم النسخ!\" : \"Copied!\" : isArabic ? \"نسخ\" : \"Copy\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            promptTemplate && value.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handlePromptCopy,\n                    className: \"flex items-center px-4 py-2 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-1\",\n                            children: \"\\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this),\n                        isArabic ? \"نسخ كـ Prompt\" : \"Copy as Prompt\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                    lineNumber: 163,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartQuestion.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartQuestion, \"23gELrRwHOSGqDEIjyy5y28Fmsk=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartQuestion;\nvar _c;\n$RefreshReg$(_c, \"SmartQuestion\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartQuestion.tsx\n"));

/***/ })

});