'use client';

import { useState, useEffect } from 'react';
import { useContextStore } from '@/store/contextStore';
import { 
  Palette, 
  Type, 
  Monitor, 
  Sun, 
  Moon, 
  Smartphone,
  Eye,
  Sliders,
  RotateCcw
} from 'lucide-react';

interface AppearanceSettings {
  theme: 'light' | 'dark' | 'auto';
  primaryColor: string;
  fontSize: 'small' | 'medium' | 'large';
  fontFamily: string;
  borderRadius: 'none' | 'small' | 'medium' | 'large';
  spacing: 'compact' | 'normal' | 'relaxed';
  accentColor: string;
}

interface AppearanceCustomizerProps {
  isArabic: boolean;
  onClose?: () => void;
}

export default function AppearanceCustomizer({ isArabic, onClose }: AppearanceCustomizerProps) {
  const { currentLanguage } = useContextStore();
  
  const [settings, setSettings] = useState<AppearanceSettings>({
    theme: 'auto',
    primaryColor: '#3b82f6',
    fontSize: 'medium',
    fontFamily: isArabic ? 'Tajawal' : 'Inter',
    borderRadius: 'medium',
    spacing: 'normal',
    accentColor: '#10b981'
  });

  const translations = {
    ar: {
      title: 'تخصيص المظهر',
      theme: 'السمة',
      light: 'فاتح',
      dark: 'داكن',
      auto: 'تلقائي',
      colors: 'الألوان',
      primaryColor: 'اللون الأساسي',
      accentColor: 'لون التمييز',
      typography: 'الطباعة',
      fontSize: 'حجم الخط',
      fontFamily: 'نوع الخط',
      small: 'صغير',
      medium: 'متوسط',
      large: 'كبير',
      layout: 'التخطيط',
      borderRadius: 'انحناء الحواف',
      spacing: 'التباعد',
      none: 'بدون',
      compact: 'مضغوط',
      normal: 'عادي',
      relaxed: 'مريح',
      presets: 'الإعدادات المسبقة',
      modern: 'حديث',
      classic: 'كلاسيكي',
      minimal: 'بسيط',
      colorful: 'ملون',
      reset: 'إعادة تعيين',
      apply: 'تطبيق',
      preview: 'معاينة'
    },
    en: {
      title: 'Appearance Customizer',
      theme: 'Theme',
      light: 'Light',
      dark: 'Dark',
      auto: 'Auto',
      colors: 'Colors',
      primaryColor: 'Primary Color',
      accentColor: 'Accent Color',
      typography: 'Typography',
      fontSize: 'Font Size',
      fontFamily: 'Font Family',
      small: 'Small',
      medium: 'Medium',
      large: 'Large',
      layout: 'Layout',
      borderRadius: 'Border Radius',
      spacing: 'Spacing',
      none: 'None',
      compact: 'Compact',
      normal: 'Normal',
      relaxed: 'Relaxed',
      presets: 'Presets',
      modern: 'Modern',
      classic: 'Classic',
      minimal: 'Minimal',
      colorful: 'Colorful',
      reset: 'Reset',
      apply: 'Apply',
      preview: 'Preview'
    }
  };

  const t = translations[isArabic ? 'ar' : 'en'];

  const colorPresets = [
    { name: 'Blue', primary: '#3b82f6', accent: '#10b981' },
    { name: 'Purple', primary: '#8b5cf6', accent: '#f59e0b' },
    { name: 'Green', primary: '#10b981', accent: '#3b82f6' },
    { name: 'Red', primary: '#ef4444', accent: '#8b5cf6' },
    { name: 'Orange', primary: '#f97316', accent: '#06b6d4' },
    { name: 'Pink', primary: '#ec4899', accent: '#84cc16' }
  ];

  const fontOptions = isArabic ? [
    { name: 'Tajawal', value: 'Tajawal' },
    { name: 'Cairo', value: 'Cairo' },
    { name: 'Amiri', value: 'Amiri' },
    { name: 'Noto Sans Arabic', value: 'Noto Sans Arabic' }
  ] : [
    { name: 'Inter', value: 'Inter' },
    { name: 'Roboto', value: 'Roboto' },
    { name: 'Open Sans', value: 'Open Sans' },
    { name: 'Poppins', value: 'Poppins' }
  ];

  const presets = {
    modern: {
      primaryColor: '#3b82f6',
      accentColor: '#10b981',
      borderRadius: 'large',
      spacing: 'normal'
    },
    classic: {
      primaryColor: '#1f2937',
      accentColor: '#dc2626',
      borderRadius: 'small',
      spacing: 'compact'
    },
    minimal: {
      primaryColor: '#6b7280',
      accentColor: '#374151',
      borderRadius: 'none',
      spacing: 'relaxed'
    },
    colorful: {
      primaryColor: '#ec4899',
      accentColor: '#f59e0b',
      borderRadius: 'medium',
      spacing: 'normal'
    }
  };

  // تطبيق الإعدادات على CSS
  useEffect(() => {
    const root = document.documentElement;
    
    // تطبيق الألوان
    root.style.setProperty('--primary-color', settings.primaryColor);
    root.style.setProperty('--accent-color', settings.accentColor);
    
    // تطبيق حجم الخط
    const fontSizes = {
      small: '14px',
      medium: '16px',
      large: '18px'
    };
    root.style.setProperty('--base-font-size', fontSizes[settings.fontSize]);
    
    // تطبيق نوع الخط
    root.style.setProperty('--font-family', settings.fontFamily);
    
    // تطبيق انحناء الحواف
    const borderRadii = {
      none: '0px',
      small: '4px',
      medium: '8px',
      large: '12px'
    };
    root.style.setProperty('--border-radius', borderRadii[settings.borderRadius]);
    
    // تطبيق التباعد
    const spacings = {
      compact: '0.75',
      normal: '1',
      relaxed: '1.25'
    };
    root.style.setProperty('--spacing-scale', spacings[settings.spacing]);
    
  }, [settings]);

  const applyPreset = (presetName: keyof typeof presets) => {
    setSettings(prev => ({
      ...prev,
      ...presets[presetName]
    }));
  };

  const resetToDefaults = () => {
    setSettings({
      theme: 'auto',
      primaryColor: '#3b82f6',
      fontSize: 'medium',
      fontFamily: isArabic ? 'Tajawal' : 'Inter',
      borderRadius: 'medium',
      spacing: 'normal',
      accentColor: '#10b981'
    });
  };

  return (
    <div className={`max-w-2xl mx-auto p-6 bg-white dark:bg-gray-900 rounded-lg shadow-xl ${isArabic ? 'rtl' : 'ltr'}`} dir={isArabic ? 'rtl' : 'ltr'}>
      {/* العنوان */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-3">
          <Palette className="w-6 h-6" />
          {t.title}
        </h2>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ×
          </button>
        )}
      </div>

      <div className="space-y-8">
        {/* السمة */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <Monitor className="w-5 h-5" />
            {t.theme}
          </h3>
          <div className="grid grid-cols-3 gap-3">
            {[
              { key: 'light', icon: Sun, label: t.light },
              { key: 'dark', icon: Moon, label: t.dark },
              { key: 'auto', icon: Smartphone, label: t.auto }
            ].map(({ key, icon: Icon, label }) => (
              <button
                key={key}
                onClick={() => setSettings(prev => ({ ...prev, theme: key as any }))}
                className={`flex items-center justify-center gap-2 p-3 rounded-lg border transition-colors ${
                  settings.theme === key
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                    : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'
                }`}
              >
                <Icon className="w-4 h-4" />
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* الألوان */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <Palette className="w-5 h-5" />
            {t.colors}
          </h3>
          
          {/* الألوان المسبقة */}
          <div className="grid grid-cols-6 gap-2 mb-4">
            {colorPresets.map((preset, index) => (
              <button
                key={index}
                onClick={() => setSettings(prev => ({ 
                  ...prev, 
                  primaryColor: preset.primary, 
                  accentColor: preset.accent 
                }))}
                className="w-12 h-12 rounded-lg border-2 border-gray-300 dark:border-gray-600 hover:scale-105 transition-transform"
                style={{ 
                  background: `linear-gradient(135deg, ${preset.primary} 0%, ${preset.accent} 100%)` 
                }}
                title={preset.name}
              />
            ))}
          </div>

          {/* منتقي الألوان */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t.primaryColor}
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={settings.primaryColor}
                  onChange={(e) => setSettings(prev => ({ ...prev, primaryColor: e.target.value }))}
                  className="w-12 h-10 rounded border border-gray-300 dark:border-gray-600"
                />
                <input
                  type="text"
                  value={settings.primaryColor}
                  onChange={(e) => setSettings(prev => ({ ...prev, primaryColor: e.target.value }))}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t.accentColor}
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={settings.accentColor}
                  onChange={(e) => setSettings(prev => ({ ...prev, accentColor: e.target.value }))}
                  className="w-12 h-10 rounded border border-gray-300 dark:border-gray-600"
                />
                <input
                  type="text"
                  value={settings.accentColor}
                  onChange={(e) => setSettings(prev => ({ ...prev, accentColor: e.target.value }))}
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              </div>
            </div>
          </div>
        </div>

        {/* الطباعة */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <Type className="w-5 h-5" />
            {t.typography}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t.fontSize}
              </label>
              <select
                value={settings.fontSize}
                onChange={(e) => setSettings(prev => ({ ...prev, fontSize: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="small">{t.small}</option>
                <option value="medium">{t.medium}</option>
                <option value="large">{t.large}</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t.fontFamily}
              </label>
              <select
                value={settings.fontFamily}
                onChange={(e) => setSettings(prev => ({ ...prev, fontFamily: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                {fontOptions.map(font => (
                  <option key={font.value} value={font.value}>{font.name}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* الإعدادات المسبقة */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
            <Eye className="w-5 h-5" />
            {t.presets}
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {Object.keys(presets).map((presetName) => (
              <button
                key={presetName}
                onClick={() => applyPreset(presetName as keyof typeof presets)}
                className="p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-center"
              >
                {t[presetName as keyof typeof t]}
              </button>
            ))}
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={resetToDefaults}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <RotateCcw className="w-4 h-4" />
            {t.reset}
          </button>
          
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t.preview}
            </div>
            <button
              onClick={() => {
                // حفظ الإعدادات في localStorage
                localStorage.setItem('contextkit-appearance', JSON.stringify(settings));
                onClose?.();
              }}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {t.apply}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
