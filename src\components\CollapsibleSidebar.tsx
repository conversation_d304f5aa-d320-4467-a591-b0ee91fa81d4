'use client';

import { useState, useEffect } from 'react';
import { 
  <PERSON>u, 
  X, 
  Search, 
  MessageSquare, 
  Clock, 
  Star,
  Trash2,
  Filter,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  isStarred: boolean;
  messageCount: number;
}

interface CollapsibleSidebarProps {
  isArabic: boolean;
  conversations: Conversation[];
  currentConversationId?: string;
  onConversationSelect: (id: string) => void;
  onNewConversation: () => void;
  onDeleteConversation: (id: string) => void;
  onToggleStarred: (id: string) => void;
}

export default function CollapsibleSidebar({
  isArabic,
  conversations,
  currentConversationId,
  onConversationSelect,
  onNewConversation,
  onDeleteConversation,
  onToggleStarred
}: CollapsibleSidebarProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'starred' | 'recent'>('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const translations = {
    ar: {
      conversations: 'المحادثات',
      search: 'البحث في المحادثات...',
      newChat: 'محادثة جديدة',
      all: 'الكل',
      starred: 'المميزة',
      recent: 'الحديثة',
      filter: 'تصفية',
      delete: 'حذف',
      star: 'إضافة للمميزة',
      unstar: 'إزالة من المميزة',
      noConversations: 'لا توجد محادثات',
      noResults: 'لا توجد نتائج للبحث'
    },
    en: {
      conversations: 'Conversations',
      search: 'Search conversations...',
      newChat: 'New Chat',
      all: 'All',
      starred: 'Starred',
      recent: 'Recent',
      filter: 'Filter',
      delete: 'Delete',
      star: 'Add to starred',
      unstar: 'Remove from starred',
      noConversations: 'No conversations',
      noResults: 'No search results'
    }
  };

  const t = translations[isArabic ? 'ar' : 'en'];

  // تصفية المحادثات حسب البحث والفلتر
  const filteredConversations = conversations.filter(conv => {
    const matchesSearch = conv.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         conv.lastMessage.toLowerCase().includes(searchQuery.toLowerCase());
    
    switch (filterType) {
      case 'starred':
        return matchesSearch && conv.isStarred;
      case 'recent':
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return matchesSearch && conv.timestamp > oneDayAgo;
      default:
        return matchesSearch;
    }
  });

  // ترتيب المحادثات حسب التاريخ
  const sortedConversations = filteredConversations.sort((a, b) => 
    b.timestamp.getTime() - a.timestamp.getTime()
  );

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return isArabic ? 'الآن' : 'Now';
    if (diffMins < 60) return isArabic ? `منذ ${diffMins} د` : `${diffMins}m`;
    if (diffHours < 24) return isArabic ? `منذ ${diffHours} س` : `${diffHours}h`;
    if (diffDays < 7) return isArabic ? `منذ ${diffDays} يوم` : `${diffDays}d`;
    
    return date.toLocaleDateString(isArabic ? 'ar' : 'en');
  };

  return (
    <>
      {/* زر فتح الشريط الجانبي */}
      <button
        onClick={() => setIsOpen(true)}
        className={`fixed top-20 ${isArabic ? 'right-4' : 'left-4'} z-40 p-3 bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors`}
        title={t.conversations}
      >
        <Menu className="w-5 h-5 text-gray-600 dark:text-gray-300" />
      </button>

      {/* الشريط الجانبي */}
      <div className={`fixed inset-y-0 ${isArabic ? 'right-0' : 'left-0'} z-50 w-80 bg-white dark:bg-gray-900 shadow-xl transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : isArabic ? 'translate-x-full' : '-translate-x-full'
      }`}>
        {/* رأس الشريط الجانبي */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t.conversations}
          </h2>
          <button
            onClick={() => setIsOpen(false)}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* شريط البحث */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className={`absolute top-3 ${isArabic ? 'right-3' : 'left-3'} w-4 h-4 text-gray-400`} />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder={t.search}
              className={`w-full ${isArabic ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent ${isArabic ? 'text-right' : 'text-left'}`}
              dir={isArabic ? 'rtl' : 'ltr'}
            />
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-3">
            <button
              onClick={onNewConversation}
              className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <MessageSquare className="w-4 h-4" />
              {t.newChat}
            </button>
          </div>

          {/* فلتر المحادثات */}
          <div className="relative">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center justify-between w-full px-3 py-2 text-sm text-gray-600 dark:text-gray-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <span className="flex items-center gap-2">
                <Filter className="w-4 h-4" />
                {t.filter}: {t[filterType]}
              </span>
              {isFilterOpen ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>

            {isFilterOpen && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10">
                {(['all', 'starred', 'recent'] as const).map((type) => (
                  <button
                    key={type}
                    onClick={() => {
                      setFilterType(type);
                      setIsFilterOpen(false);
                    }}
                    className={`w-full px-3 py-2 text-sm text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      filterType === type ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'
                    }`}
                  >
                    {t[type]}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* قائمة المحادثات */}
        <div className="flex-1 overflow-y-auto">
          {sortedConversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
              <MessageSquare className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>{searchQuery ? t.noResults : t.noConversations}</p>
            </div>
          ) : (
            <div className="p-2">
              {sortedConversations.map((conversation) => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  isActive={conversation.id === currentConversationId}
                  isArabic={isArabic}
                  onSelect={() => onConversationSelect(conversation.id)}
                  onDelete={() => onDeleteConversation(conversation.id)}
                  onToggleStarred={() => onToggleStarred(conversation.id)}
                  formatTimestamp={formatTimestamp}
                  translations={t}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* خلفية شفافة لإغلاق الشريط الجانبي */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
}

interface ConversationItemProps {
  conversation: Conversation;
  isActive: boolean;
  isArabic: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onToggleStarred: () => void;
  formatTimestamp: (date: Date) => string;
  translations: any;
}

function ConversationItem({
  conversation,
  isActive,
  isArabic,
  onSelect,
  onDelete,
  onToggleStarred,
  formatTimestamp,
  translations
}: ConversationItemProps) {
  const [showActions, setShowActions] = useState(false);

  return (
    <div
      className={`relative p-3 rounded-lg cursor-pointer transition-colors group ${
        isActive 
          ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
      }`}
      onClick={onSelect}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            {conversation.isStarred && (
              <Star className="w-3 h-3 text-yellow-500 fill-current" />
            )}
            <h3 className={`font-medium text-gray-900 dark:text-white truncate ${isArabic ? 'text-right' : 'text-left'}`}>
              {conversation.title}
            </h3>
          </div>
          <p className={`text-sm text-gray-500 dark:text-gray-400 truncate ${isArabic ? 'text-right' : 'text-left'}`}>
            {conversation.lastMessage}
          </p>
          <div className="flex items-center justify-between mt-2">
            <span className="text-xs text-gray-400">
              {formatTimestamp(conversation.timestamp)}
            </span>
            <span className="text-xs text-gray-400">
              {conversation.messageCount} {isArabic ? 'رسالة' : 'messages'}
            </span>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        {showActions && (
          <div className={`flex items-center gap-1 ${isArabic ? 'mr-2' : 'ml-2'}`}>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onToggleStarred();
              }}
              className="p-1 text-gray-400 hover:text-yellow-500 transition-colors"
              title={conversation.isStarred ? translations.unstar : translations.star}
            >
              <Star className={`w-4 h-4 ${conversation.isStarred ? 'fill-current text-yellow-500' : ''}`} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="p-1 text-gray-400 hover:text-red-500 transition-colors"
              title={translations.delete}
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
