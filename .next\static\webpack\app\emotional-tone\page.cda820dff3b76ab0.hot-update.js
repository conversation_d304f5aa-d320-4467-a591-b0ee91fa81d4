"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/emotional-tone/page",{

/***/ "(app-pages-browser)/./src/components/SmartFieldAssistant.tsx":
/*!************************************************!*\
  !*** ./src/components/SmartFieldAssistant.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SmartFieldAssistant; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(app-pages-browser)/./src/store/contextStore.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles,Wand2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction SmartFieldAssistant(param) {\n    let { fieldName, fieldValue, onValueChange, placeholder, context, className = \"\" } = param;\n    _s();\n    const { currentLanguage, getActiveProviders, getAllData } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generatedSuggestions, setGeneratedSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [copiedIndex, setCopiedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeProviders, setActiveProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    // تجنب مشاكل الهيدريشن\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        setActiveProviders(getActiveProviders());\n    }, [\n        getActiveProviders\n    ]);\n    // تحقق من وجود مقدم خدمة صالح\n    const hasValidProvider = mounted && activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const translations = {\n        generateWithAI: isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\",\n        generating: isArabic ? \"جاري التوليد...\" : \"Generating...\",\n        suggestions: isArabic ? \"اقتراحات ذكية\" : \"Smart Suggestions\",\n        useThis: isArabic ? \"استخدام هذا\" : \"Use This\",\n        copy: isArabic ? \"نسخ\" : \"Copy\",\n        copied: isArabic ? \"تم النسخ\" : \"Copied\",\n        noProviders: isArabic ? \"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً\" : \"Please configure an AI provider and select models in Settings first\",\n        error: isArabic ? \"حدث خطأ أثناء التوليد\" : \"Error occurred during generation\",\n        tryAgain: isArabic ? \"حاول مرة أخرى\" : \"Try Again\",\n        regenerate: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n        fastGeneration: isArabic ? \"توليد سريع (محسّن)\" : \"Fast Generation (Optimized)\",\n        timeout: isArabic ? \"انتهت مهلة الطلب - حاول مرة أخرى\" : \"Request timeout - try again\"\n    };\n    const generateSuggestions = async ()=>{\n        if (!hasValidProvider) {\n            console.warn(\"No valid provider available:\", {\n                activeProviders,\n                hasValidProvider\n            });\n            alert(translations.noProviders);\n            return;\n        }\n        setIsGenerating(true);\n        try {\n            const allContext = getAllData();\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            console.log(\"Using provider:\", provider === null || provider === void 0 ? void 0 : provider.name, \"with model:\", provider === null || provider === void 0 ? void 0 : provider.selectedModels[0]);\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            // إنشاء prompt ذكي بناءً على السياق والحقل\n            const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);\n            console.log(\"Generated prompt:\", prompt);\n            const requestBody = {\n                providerId: provider.id,\n                apiKey: provider.apiKey,\n                model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                messages: [\n                    {\n                        role: \"user\",\n                        content: prompt\n                    }\n                ],\n                context: allContext,\n                fieldName,\n                language: currentLanguage,\n                temperature: 0.7,\n                maxTokens: 200 // تقليل maxTokens بشكل كبير للسرعة\n            };\n            console.log(\"Sending request to API:\", requestBody);\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(requestBody)\n            });\n            console.log(\"API Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"API Error:\", errorText);\n                throw new Error(\"API Error: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                // استخدام أول اقتراح مباشرة في خانة الكتابة\n                const suggestions = parseSuggestions(result.content);\n                console.log(\"Parsed suggestions:\", suggestions);\n                if (suggestions.length > 0) {\n                    // وضع أول اقتراح في خانة الكتابة\n                    onValueChange(suggestions[0]);\n                    // حفظ باقي الاقتراحات للاستخدام لاحقاً\n                    setGeneratedSuggestions(suggestions);\n                } else {\n                    const errorMsg = isArabic ? \"لم يتم العثور على اقتراحات مناسبة\" : \"No suitable suggestions found\";\n                    onValueChange(errorMsg);\n                }\n            } else {\n                throw new Error(result.error || \"Generation failed\");\n            }\n        } catch (error) {\n            console.error(\"Generation error:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Unknown error\";\n            onValueChange(\"\".concat(translations.error, \": \").concat(errorMessage));\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const createSmartPrompt = (fieldName, currentValue, context, isArabic)=>{\n        const fieldPrompts = {\n            name: {\n                ar: \"اقترح 3 أسماء مناسبة للمشروع\",\n                en: \"Suggest 3 suitable project names\"\n            },\n            purpose: {\n                ar: \"اكتب 3 أوصاف مختلفة لغرض المشروع\",\n                en: \"Write 3 different project purpose descriptions\"\n            },\n            targetUsers: {\n                ar: \"حدد 3 مجموعات من المستخدمين المستهدفين\",\n                en: \"Define 3 target user groups\"\n            },\n            goals: {\n                ar: \"اقترح 3 أهداف للمشروع\",\n                en: \"Suggest 3 project goals\"\n            }\n        };\n        const fieldPrompt = fieldPrompts[fieldName];\n        const basePrompt = fieldPrompt ? isArabic ? fieldPrompt.ar : fieldPrompt.en : isArabic ? \"اقترح محتوى لـ \".concat(fieldName) : \"Suggest content for \".concat(fieldName);\n        // تبسيط السياق - أخذ المعلومات المهمة فقط\n        const relevantInfo = (context === null || context === void 0 ? void 0 : context.name) || (context === null || context === void 0 ? void 0 : context.purpose) || \"\";\n        const contextInfo = relevantInfo ? isArabic ? \"المشروع: \".concat(relevantInfo.substring(0, 50)) : \"Project: \".concat(relevantInfo.substring(0, 50)) : \"\";\n        const instructions = isArabic ? \"قدم 3 اقتراحات مرقمة، كل اقتراح في سطر منفصل.\" : \"Provide 3 numbered suggestions, each on a separate line.\";\n        return \"\".concat(contextInfo, \"\\n\").concat(basePrompt, \"\\n\").concat(instructions);\n    };\n    const parseSuggestions = (content)=>{\n        // تقسيم المحتوى إلى اقتراحات منفصلة\n        const lines = content.split(\"\\n\").filter((line)=>line.trim());\n        const suggestions = [];\n        for (const line of lines){\n            // البحث عن الأسطر المرقمة أو التي تبدأ برقم\n            if (/^\\d+[.\\-\\)]\\s*/.test(line.trim()) || /^[•\\-\\*]\\s*/.test(line.trim())) {\n                const cleaned = line.replace(/^\\d+[.\\-\\)]\\s*/, \"\").replace(/^[•\\-\\*]\\s*/, \"\").trim();\n                if (cleaned && cleaned.length > 10) {\n                    suggestions.push(cleaned);\n                }\n            } else if (line.trim().length > 20 && !line.includes(\":\") && suggestions.length < 3) {\n                suggestions.push(line.trim());\n            }\n        }\n        // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل\n        if (suggestions.length === 0) {\n            const sentences = content.split(/[.!?]+/).filter((s)=>s.trim().length > 20);\n            return sentences.slice(0, 3).map((s)=>s.trim());\n        }\n        return suggestions.slice(0, 3);\n    };\n    const copyToClipboard = async (text, index)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopiedIndex(index);\n            setTimeout(()=>setCopiedIndex(null), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy:\", error);\n        }\n    };\n    const regenerateContent = async ()=>{\n        if (generatedSuggestions.length > 1) {\n            // استخدام الاقتراح التالي إذا كان متوفراً\n            const currentIndex = generatedSuggestions.findIndex((s)=>s === fieldValue);\n            const nextIndex = (currentIndex + 1) % generatedSuggestions.length;\n            onValueChange(generatedSuggestions[nextIndex]);\n        } else {\n            // توليد محتوى جديد\n            await generateSuggestions();\n        }\n    };\n    // تجنب مشاكل الهيدريشن\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                disabled: true,\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 cursor-not-allowed font-arabic\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this),\n                    isArabic ? \"\\uD83D\\uDCC4 توليد بالذكاء الاصطناعي\" : \"\\uD83D\\uDCC4 Generate with AI\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center gap-2 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: generateSuggestions,\n                disabled: isGenerating || !hasValidProvider,\n                className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group \".concat(hasValidProvider ? \"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95\" : \"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30\", \" \").concat(isGenerating ? \"animate-pulse scale-105\" : \"\"),\n                title: hasValidProvider ? translations.generateWithAI : translations.noProviders,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    hasValidProvider && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-2\",\n                        children: [\n                            isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-4 h-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-12 transition-transform duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isGenerating ? translations.generating : translations.generateWithAI\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            fieldValue && generatedSuggestions.length > 0 && !isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: regenerateContent,\n                className: \"relative flex items-center gap-2 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-orange-500/80 to-red-500/80 hover:from-orange-600/90 hover:to-red-600/90 text-white shadow-lg hover:shadow-xl hover:shadow-orange-500/25 hover:scale-105 active:scale-95\",\n                title: isArabic ? \"إعادة توليد\" : \"Regenerate\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4 group-hover:rotate-180 transition-transform duration-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-arabic font-medium\",\n                                children: isArabic ? \"إعادة توليد\" : \"Regenerate\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\SmartFieldAssistant.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartFieldAssistant, \"ZrnkxJH8z8I5/fadLS+VIi/I8fM=\", false, function() {\n    return [\n        _store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore\n    ];\n});\n_c = SmartFieldAssistant;\nvar _c;\n$RefreshReg$(_c, \"SmartFieldAssistant\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SmartFieldAssistant.tsx\n"));

/***/ })

});