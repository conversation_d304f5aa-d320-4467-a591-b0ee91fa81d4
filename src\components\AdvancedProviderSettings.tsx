'use client';

import { useState } from 'react';
import { ProviderConfig } from '@/store/contextStore';
import { 
  Settings, 
  Star, 
  Shield, 
  Clock, 
  RefreshCw, 
  DollarSign,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

interface AdvancedProviderSettingsProps {
  provider: ProviderConfig;
  onUpdate: (updates: Partial<ProviderConfig>) => void;
  isArabic: boolean;
}

export default function AdvancedProviderSettings({ 
  provider, 
  onUpdate, 
  isArabic 
}: AdvancedProviderSettingsProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const translations = {
    ar: {
      advancedSettings: 'الإعدادات المتقدمة',
      priority: 'الأولوية',
      priorityDesc: 'أولوية المزود (1-10، 10 هو الأعلى)',
      isBackup: 'مزود احتياطي',
      backupDesc: 'استخدم هذا المزود كنسخة احتياطية فقط',
      maxRequests: 'الحد الأقصى للطلبات/دقيقة',
      timeout: 'مهلة الطلب (ثانية)',
      retryAttempts: 'محاولات الإعادة',
      costPerToken: 'التكلفة لكل رمز ($)',
      save: 'حفظ',
      cancel: 'إلغاء'
    },
    en: {
      advancedSettings: 'Advanced Settings',
      priority: 'Priority',
      priorityDesc: 'Provider priority (1-10, 10 is highest)',
      isBackup: 'Backup Provider',
      backupDesc: 'Use this provider as backup only',
      maxRequests: 'Max Requests/Minute',
      timeout: 'Request Timeout (seconds)',
      retryAttempts: 'Retry Attempts',
      costPerToken: 'Cost per Token ($)',
      save: 'Save',
      cancel: 'Cancel'
    }
  };

  const t = translations[isArabic ? 'ar' : 'en'];

  const [localSettings, setLocalSettings] = useState({
    priority: provider.priority || 5,
    isBackup: provider.isBackup || false,
    maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,
    timeout: provider.timeout || 30,
    retryAttempts: provider.retryAttempts || 3,
    costPerToken: provider.costPerToken || 0
  });

  const handleSave = () => {
    onUpdate(localSettings);
    setIsExpanded(false);
  };

  const handleCancel = () => {
    setLocalSettings({
      priority: provider.priority || 5,
      isBackup: provider.isBackup || false,
      maxRequestsPerMinute: provider.maxRequestsPerMinute || 60,
      timeout: provider.timeout || 30,
      retryAttempts: provider.retryAttempts || 3,
      costPerToken: provider.costPerToken || 0
    });
    setIsExpanded(false);
  };

  return (
    <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full text-left"
      >
        <div className="flex items-center gap-2">
          <Settings className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {t.advancedSettings}
          </span>
        </div>
        {isExpanded ? (
          <ChevronUp className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronDown className="w-4 h-4 text-gray-500" />
        )}
      </button>

      {isExpanded && (
        <div className="mt-4 space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          {/* الأولوية */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Star className="w-4 h-4" />
                {t.priority}
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={localSettings.priority}
                onChange={(e) => setLocalSettings(prev => ({ 
                  ...prev, 
                  priority: parseInt(e.target.value) || 5 
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {t.priorityDesc}
              </p>
            </div>

            {/* مزود احتياطي */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Shield className="w-4 h-4" />
                {t.isBackup}
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={localSettings.isBackup}
                  onChange={(e) => setLocalSettings(prev => ({ 
                    ...prev, 
                    isBackup: e.target.checked 
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {t.backupDesc}
                </span>
              </label>
            </div>
          </div>

          {/* الحد الأقصى للطلبات ومهلة الطلب */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Clock className="w-4 h-4" />
                {t.maxRequests}
              </label>
              <input
                type="number"
                min="1"
                max="1000"
                value={localSettings.maxRequestsPerMinute}
                onChange={(e) => setLocalSettings(prev => ({ 
                  ...prev, 
                  maxRequestsPerMinute: parseInt(e.target.value) || 60 
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Clock className="w-4 h-4" />
                {t.timeout}
              </label>
              <input
                type="number"
                min="5"
                max="300"
                value={localSettings.timeout}
                onChange={(e) => setLocalSettings(prev => ({ 
                  ...prev, 
                  timeout: parseInt(e.target.value) || 30 
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* محاولات الإعادة والتكلفة */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <RefreshCw className="w-4 h-4" />
                {t.retryAttempts}
              </label>
              <input
                type="number"
                min="0"
                max="10"
                value={localSettings.retryAttempts}
                onChange={(e) => setLocalSettings(prev => ({ 
                  ...prev, 
                  retryAttempts: parseInt(e.target.value) || 3 
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <DollarSign className="w-4 h-4" />
                {t.costPerToken}
              </label>
              <input
                type="number"
                step="0.000001"
                min="0"
                value={localSettings.costPerToken}
                onChange={(e) => setLocalSettings(prev => ({ 
                  ...prev, 
                  costPerToken: parseFloat(e.target.value) || 0 
                }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* أزرار الحفظ والإلغاء */}
          <div className="flex items-center justify-end gap-2 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              {t.cancel}
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              {t.save}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
