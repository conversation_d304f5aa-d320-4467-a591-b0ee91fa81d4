import { NextRequest, NextResponse } from 'next/server';
import { getProviderById, LLM_PROVIDERS_DATABASE } from '@/lib/llmProviders';

/**
 * API لجلب النماذج المتاحة من مقدمي الخدمة
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const providerId = searchParams.get('providerId');

    if (providerId) {
      // جلب نماذج مقدم خدمة محدد
      const provider = getProviderById(providerId);
      if (!provider) {
        return NextResponse.json(
          { error: 'Provider not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        provider: {
          id: provider.id,
          name: provider.name,
          icon: provider.icon,
          description: provider.description
        },
        models: provider.models
      });
    } else {
      // جلب جميع المقدمين والنماذج
      const providersWithModels = LLM_PROVIDERS_DATABASE.map(provider => ({
        id: provider.id,
        name: provider.name,
        icon: provider.icon,
        description: provider.description,
        isActive: provider.isActive,
        modelCount: provider.models.length,
        models: provider.models
      }));

      return NextResponse.json({
        providers: providersWithModels,
        totalProviders: LLM_PROVIDERS_DATABASE.length,
        totalModels: LLM_PROVIDERS_DATABASE.reduce((sum, p) => sum + p.models.length, 0)
      });
    }
  } catch (error) {
    console.error('Models API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * API لإضافة نماذج مخصصة أو تحديث معلومات النماذج
 */
export async function POST(request: NextRequest) {
  try {
    const { providerId, apiKey, baseUrl } = await request.json();

    if (!providerId || !apiKey) {
      return NextResponse.json(
        { error: 'Provider ID and API key are required' },
        { status: 400 }
      );
    }

    const provider = getProviderById(providerId);
    if (!provider) {
      return NextResponse.json(
        { error: 'Provider not found' },
        { status: 404 }
      );
    }

    // جلب النماذج المتاحة من API المقدم
    const availableModels = await fetchAvailableModels(providerId, apiKey, baseUrl || provider.baseUrl);

    if (availableModels.success) {
      return NextResponse.json({
        success: true,
        provider: {
          id: provider.id,
          name: provider.name,
          icon: provider.icon
        },
        models: availableModels.models,
        defaultModels: provider.models // النماذج الافتراضية من قاعدة البيانات
      });
    } else {
      return NextResponse.json({
        success: false,
        error: availableModels.error,
        defaultModels: provider.models // إرجاع النماذج الافتراضية في حالة الفشل
      });
    }
  } catch (error) {
    console.error('Models fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function fetchAvailableModels(providerId: string, apiKey: string, baseUrl: string) {
  try {
    let response;
    let models: any[] = [];

    switch (providerId) {
      case 'openai':
      case 'openrouter':
      case 'deepseek':
      case 'groq':
      case 'mistral':
        response = await fetch(`${baseUrl}/models`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          models = data.data?.map((model: any) => ({
            id: model.id,
            name: model.id,
            description: model.description || `${model.id} model`,
            contextLength: model.context_length || 4096,
            created: model.created,
            owned_by: model.owned_by
          })) || [];
        }
        break;

      case 'google':
        response = await fetch(`${baseUrl}/models?key=${apiKey}`);
        
        if (response.ok) {
          const data = await response.json();
          models = data.models?.map((model: any) => ({
            id: model.name.replace('models/', ''),
            name: model.displayName || model.name,
            description: model.description || `${model.name} model`,
            contextLength: model.inputTokenLimit || 32768,
            supportedGenerationMethods: model.supportedGenerationMethods
          })) || [];
        }
        break;

      case 'anthropic':
        // Anthropic doesn't have a models endpoint, return predefined models
        const anthropicProvider = getProviderById('anthropic');
        models = anthropicProvider?.models || [];
        break;

      case 'cohere':
        response = await fetch(`${baseUrl}/models`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          models = data.models?.map((model: any) => ({
            id: model.name,
            name: model.name,
            description: model.description || `${model.name} model`,
            contextLength: model.context_length || 4096,
            endpoints: model.endpoints
          })) || [];
        } else {
          // Fallback to predefined models if API fails
          const cohereProvider = getProviderById('cohere');
          models = cohereProvider?.models || [];
        }
        break;

      default:
        // Try generic OpenAI-compatible endpoint
        response = await fetch(`${baseUrl}/models`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          models = data.data?.map((model: any) => ({
            id: model.id,
            name: model.id,
            description: model.description || `${model.id} model`,
            contextLength: model.context_length || 4096
          })) || [];
        }
    }

    if (models.length > 0) {
      return {
        success: true,
        models: models
      };
    } else {
      return {
        success: false,
        error: 'No models found or API call failed'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * API للبحث في النماذج
 */
export async function PUT(request: NextRequest) {
  try {
    const { query, providerId } = await request.json();

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    const lowercaseQuery = query.toLowerCase();
    let searchResults: any[] = [];

    if (providerId) {
      // البحث في مقدم خدمة محدد
      const provider = getProviderById(providerId);
      if (provider) {
        searchResults = provider.models.filter(model =>
          model.name.toLowerCase().includes(lowercaseQuery) ||
          model.description.toLowerCase().includes(lowercaseQuery) ||
          model.id.toLowerCase().includes(lowercaseQuery)
        ).map(model => ({
          ...model,
          provider: {
            id: provider.id,
            name: provider.name,
            icon: provider.icon
          }
        }));
      }
    } else {
      // البحث في جميع المقدمين
      for (const provider of LLM_PROVIDERS_DATABASE) {
        const matchingModels = provider.models.filter(model =>
          model.name.toLowerCase().includes(lowercaseQuery) ||
          model.description.toLowerCase().includes(lowercaseQuery) ||
          model.id.toLowerCase().includes(lowercaseQuery)
        ).map(model => ({
          ...model,
          provider: {
            id: provider.id,
            name: provider.name,
            icon: provider.icon
          }
        }));

        searchResults.push(...matchingModels);
      }
    }

    return NextResponse.json({
      query,
      results: searchResults,
      totalResults: searchResults.length
    });
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
